package com.sankuai.dzusergrowth.operation.domain.model.query;

import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 数据条目查询条件
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataItemQuery {
    
    /**
     * 条目ID列表
     */
    private List<Long> itemIds;
    
    /**
     * 数据集ID
     */
    private Long datasetId;
    
    /**
     * 数据集ID列表
     */
    private List<Long> datasetIds;
    
    /**
     * 数据唯一键
     */
    private String dataUniqueKey;
    
    /**
     * 是否包含已删除数据（默认false，不包含）
     */
    private Boolean includeDeleted;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建时间开始
     */
    private Date addTimeStart;
    
    /**
     * 创建时间结束
     */
    private Date addTimeEnd;
    
    /**
     * 分页页码（从1开始）
     */
    private Integer pageNum;
    
    /**
     * 分页大小
     */
    private Integer pageSize;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向: ASC/DESC
     */
    private String orderDirection;


    /**
     * 获取itemIds
     *
     * @return itemIds
     */
    public List<Long> getItemIds() {
        return itemIds;
    }

    /**
     * 获取datasetId
     *
     * @return datasetId
     */
    public Long getDatasetId() {
        return datasetId;
    }

    /**
     * 获取datasetIds
     *
     * @return datasetIds
     */
    public List<Long> getDatasetIds() {
        return datasetIds;
    }

    /**
     * 获取dataUniqueKey
     *
     * @return dataUniqueKey
     */
    public String getDataUniqueKey() {
        return dataUniqueKey;
    }

    /**
     * 获取includeDeleted
     *
     * @return includeDeleted
     */
    public Boolean isIncludeDeleted() {
        return includeDeleted;
    }

    /**
     * 获取creatorId
     *
     * @return creatorId
     */
    public Long getCreatorId() {
        return creatorId;
    }

    /**
     * 获取addTimeStart
     *
     * @return addTimeStart
     */
    public Date getAddTimeStart() {
        return addTimeStart;
    }

    /**
     * 获取addTimeEnd
     *
     * @return addTimeEnd
     */
    public Date getAddTimeEnd() {
        return addTimeEnd;
    }

    /**
     * 获取pageNum
     *
     * @return pageNum
     */
    public Integer getPageNum() {
        return pageNum;
    }

    /**
     * 获取pageSize
     *
     * @return pageSize
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * 获取orderBy
     *
     * @return orderBy
     */
    public String getOrderBy() {
        return orderBy;
    }

    /**
     * 获取orderDirection
     *
     * @return orderDirection
     */
    public String getOrderDirection() {
        return orderDirection;
    }

    /**
     * 设置itemIds
     *
     * @param itemIds itemIds
     */
    public void setItemIds(List<Long> itemIds) {
        this.itemIds = itemIds;
    }

    /**
     * 设置datasetId
     *
     * @param datasetId datasetId
     */
    public void setDatasetId(Long datasetId) {
        this.datasetId = datasetId;
    }

    /**
     * 设置datasetIds
     *
     * @param datasetIds datasetIds
     */
    public void setDatasetIds(List<Long> datasetIds) {
        this.datasetIds = datasetIds;
    }

    /**
     * 设置dataUniqueKey
     *
     * @param dataUniqueKey dataUniqueKey
     */
    public void setDataUniqueKey(String dataUniqueKey) {
        this.dataUniqueKey = dataUniqueKey;
    }

    /**
     * 设置includeDeleted
     *
     * @param includeDeleted includeDeleted
     */
    public void setIncludeDeleted(Boolean includeDeleted) {
        this.includeDeleted = includeDeleted;
    }

    /**
     * 设置creatorId
     *
     * @param creatorId creatorId
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 设置addTimeStart
     *
     * @param addTimeStart addTimeStart
     */
    public void setAddTimeStart(Date addTimeStart) {
        this.addTimeStart = addTimeStart;
    }

    /**
     * 设置addTimeEnd
     *
     * @param addTimeEnd addTimeEnd
     */
    public void setAddTimeEnd(Date addTimeEnd) {
        this.addTimeEnd = addTimeEnd;
    }

    /**
     * 设置pageNum
     *
     * @param pageNum pageNum
     */
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    /**
     * 设置pageSize
     *
     * @param pageSize pageSize
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 设置orderBy
     *
     * @param orderBy orderBy
     */
    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    /**
     * 设置orderDirection
     *
     * @param orderDirection orderDirection
     */
    public void setOrderDirection(String orderDirection) {
        this.orderDirection = orderDirection;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static DataItemQueryBuilder builder() {
        return new DataItemQueryBuilder();
    }
    
    /**
     * Builder类
     */
    public static class DataItemQueryBuilder {
        private List<Long> itemIds;
        private Long datasetId;
        private List<Long> datasetIds;
        private String dataUniqueKey;
        private Boolean includeDeleted;
        private Long creatorId;
        private Date addTimeStart;
        private Date addTimeEnd;
        private Integer pageNum;
        private Integer pageSize;
        private String orderBy;
        private String orderDirection;
        
        public DataItemQueryBuilder itemIds(List<Long> itemIds) {
            this.itemIds = itemIds;
            return this;
        }
        public DataItemQueryBuilder datasetId(Long datasetId) {
            this.datasetId = datasetId;
            return this;
        }
        public DataItemQueryBuilder datasetIds(List<Long> datasetIds) {
            this.datasetIds = datasetIds;
            return this;
        }
        public DataItemQueryBuilder dataUniqueKey(String dataUniqueKey) {
            this.dataUniqueKey = dataUniqueKey;
            return this;
        }
        public DataItemQueryBuilder includeDeleted(Boolean includeDeleted) {
            this.includeDeleted = includeDeleted;
            return this;
        }
        public DataItemQueryBuilder creatorId(Long creatorId) {
            this.creatorId = creatorId;
            return this;
        }
        public DataItemQueryBuilder addTimeStart(Date addTimeStart) {
            this.addTimeStart = addTimeStart;
            return this;
        }
        public DataItemQueryBuilder addTimeEnd(Date addTimeEnd) {
            this.addTimeEnd = addTimeEnd;
            return this;
        }
        public DataItemQueryBuilder pageNum(Integer pageNum) {
            this.pageNum = pageNum;
            return this;
        }
        public DataItemQueryBuilder pageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }
        public DataItemQueryBuilder orderBy(String orderBy) {
            this.orderBy = orderBy;
            return this;
        }
        public DataItemQueryBuilder orderDirection(String orderDirection) {
            this.orderDirection = orderDirection;
            return this;
        }
        
        public DataItemQuery build() {
            return new DataItemQuery(itemIds, datasetId, datasetIds, dataUniqueKey, includeDeleted, creatorId, addTimeStart, addTimeEnd, pageNum, pageSize, orderBy, orderDirection);
        }
    }
} 