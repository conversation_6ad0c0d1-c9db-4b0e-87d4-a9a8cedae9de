package com.sankuai.dzusergrowth.operation.domain.model.request;

import com.sankuai.dzusergrowth.operation.api.enums.DataTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.ColumnTypeEnum;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetColumnConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据集列信息创建请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetColumnCreateRequest {
    
    /**
     * 数据集ID（必填）
     */
    private Long datasetId;
    
    /**
     * 列名（必填）
     */
    private String name;
    
    /**
     * 列展示名（可选）
     */
    private String displayName;
    
    /**
     * 数据类型（必填）
     */
    private DataTypeEnum dataType;
    
    /**
     * 列类型（必填）
     */
    private ColumnTypeEnum columnType;
    
    /**
     * 列配置对象（可选）
     */
    private DatasetColumnConfig columnConfig;
    
    /**
     * 创建人ID（必填）
     */
    private Long creatorId;
    
    /**
     * 验证创建请求参数有效性
     */
    public boolean isValid() {
        // 必填字段验证
        if (datasetId == null) {
            return false;
        }
        
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        
        if (dataType == null) {
            return false;
        }
        
        if (columnType == null) {
            return false;
        }
        
        if (creatorId == null) {
            return false;
        }

        return true;
    }
    
    /**
     * 验证数据集ID是否有效
     */
    public boolean hasValidDatasetId() {
        return datasetId != null && datasetId > 0;
    }
    
    /**
     * 验证列名格式
     */
    public boolean hasValidName() {
        return name != null && !name.trim().isEmpty() && name.length() <= 100;
    }
    
    /**
     * 验证展示名格式（如果提供）
     */
    public boolean hasValidDisplayName() {
        return displayName == null || (!displayName.trim().isEmpty() && displayName.length() <= 200);
    }
    
    /**
     * 是否提供了展示名
     */
    public boolean hasDisplayName() {
        return displayName != null && !displayName.trim().isEmpty();
    }
    
    /**
     * 获取实际的展示名（如果没有提供展示名，则使用列名）
     */
    public String getActualDisplayName() {
        return hasDisplayName() ? displayName : name;
    }


    /**
     * 设置datasetId
     *
     * @param datasetId datasetId
     */
    public void setDatasetId(Long datasetId) {
        this.datasetId = datasetId;
    }

    /**
     * 设置name
     *
     * @param name name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 设置displayName
     *
     * @param displayName displayName
     */
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    /**
     * 设置dataType
     *
     * @param dataType dataType
     */
    public void setDataType(DataTypeEnum dataType) {
        this.dataType = dataType;
    }

    /**
     * 设置columnType
     *
     * @param columnType columnType
     */
    public void setColumnType(ColumnTypeEnum columnType) {
        this.columnType = columnType;
    }

    /**
     * 设置columnConfig
     *
     * @param columnConfig columnConfig
     */
    public void setColumnConfig(DatasetColumnConfig columnConfig) {
        this.columnConfig = columnConfig;
    }

    /**
     * 设置creatorId
     *
     * @param creatorId creatorId
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static DatasetColumnCreateRequestBuilder builder() {
        return new DatasetColumnCreateRequestBuilder();
    }
    
    /**
     * Builder类
     */
    public static class DatasetColumnCreateRequestBuilder {
        private Long datasetId;
        private String name;
        private String displayName;
        private DataTypeEnum dataType;
        private ColumnTypeEnum columnType;
        private DatasetColumnConfig columnConfig;
        private Long creatorId;
        
        public DatasetColumnCreateRequestBuilder datasetId(Long datasetId) {
            this.datasetId = datasetId;
            return this;
        }
        public DatasetColumnCreateRequestBuilder name(String name) {
            this.name = name;
            return this;
        }
        public DatasetColumnCreateRequestBuilder displayName(String displayName) {
            this.displayName = displayName;
            return this;
        }
        public DatasetColumnCreateRequestBuilder dataType(DataTypeEnum dataType) {
            this.dataType = dataType;
            return this;
        }
        public DatasetColumnCreateRequestBuilder columnType(ColumnTypeEnum columnType) {
            this.columnType = columnType;
            return this;
        }
        public DatasetColumnCreateRequestBuilder columnConfig(DatasetColumnConfig columnConfig) {
            this.columnConfig = columnConfig;
            return this;
        }
        public DatasetColumnCreateRequestBuilder creatorId(Long creatorId) {
            this.creatorId = creatorId;
            return this;
        }
        
        public DatasetColumnCreateRequest build() {
            return new DatasetColumnCreateRequest(datasetId, name, displayName, dataType, columnType, columnConfig, creatorId);
        }
    }
} 