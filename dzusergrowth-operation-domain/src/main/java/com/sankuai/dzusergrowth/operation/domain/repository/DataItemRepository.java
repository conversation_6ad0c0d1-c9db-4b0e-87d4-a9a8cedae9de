package com.sankuai.dzusergrowth.operation.domain.repository;

import com.sankuai.dzusergrowth.operation.domain.model.dataset.DataItemDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.DataItemQuery;

import java.util.List;

/**
 * 数据条目仓储接口
 */
public interface DataItemRepository {
    
    /**
     * 创建新的数据条目
     * 
     * @param dataItemDO 数据条目信息DO
     */
    void createDataItem(DataItemDO dataItemDO);
    
    /**
     * 根据ID获取数据条目
     * 
     * @param itemId 条目ID
     * @return 数据条目信息，如不存在则返回null
     */
    DataItemDO getDataItemById(Long itemId);
    
    /**
     * 更新数据条目
     * 
     * @param dataItemDO 数据条目DO
     */
    void updateDataItem(DataItemDO dataItemDO);
    
    /**
     * 软删除数据条目
     * 
     * @param itemId 条目ID
     */
    void softDeleteDataItem(Long itemId);
    
    /**
     * 批量查询数据条目
     * 
     * @param query 数据条目查询条件
     * @return 数据条目DO列表
     */
    List<DataItemDO> queryDataItem(DataItemQuery query);
    
    /**
     * 统计符合条件的数据条目数量（用于分页查询）
     * 
     * @param query 数据条目查询条件
     * @return 数据条目数量
     */
    long countDataItem(DataItemQuery query);
} 