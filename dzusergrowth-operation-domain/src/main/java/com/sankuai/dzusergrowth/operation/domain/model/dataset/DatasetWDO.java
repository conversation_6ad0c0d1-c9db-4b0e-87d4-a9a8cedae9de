package com.sankuai.dzusergrowth.operation.domain.model.dataset;

import com.sankuai.dzusergrowth.operation.api.enums.DataGranularityEnum;
import com.sankuai.dzusergrowth.operation.api.enums.DatasetTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.GenerateTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.TaskStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 数据集聚合根
 * 包含数据集基本信息和相关的列信息
 * 
 * <AUTHOR>
 * @date 2025/7/1 10:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetWDO {
    
    /**
     * 数据集ID
     */
    private Long id;

    /**
     * 数据集类型
     */
    private DatasetTypeEnum datasetType;

    /**
     * 数据粒度
     */
    private DataGranularityEnum dataGranularity;

    /**
     * 数据集名称
     */
    private String name;

    /**
     * 生成方式
     */
    private GenerateTypeEnum generateType;

    /**
     * 生成配置对象
     */
    private DatasetGenerateConfig generateConfig;

    /**
     * 任务状态
     */
    private TaskStatusEnum taskStatus;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 数据集描述
     */
    private String description;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 全量列信息
     */
    private List<DatasetColumnInfoDO> columnInfoList;
    
    /**
     * 根据DatasetDO构建聚合根的静态工厂方法
     *
     * @param datasetDO 数据集DO
     * @param columnInfoList 列信息列表
     * @return 数据集聚合根
     */
    public static DatasetWDO buildFromDatasetDO(DatasetDO datasetDO, List<DatasetColumnInfoDO> columnInfoList) {
        if (datasetDO == null) {
            return null;
        }

        return DatasetWDO.builder()
                .id(datasetDO.getId())
                .datasetType(datasetDO.getDatasetType())
                .dataGranularity(datasetDO.getDataGranularity())
                .name(datasetDO.getName())
                .generateType(datasetDO.getGenerateType())
                .generateConfig(datasetDO.getGenerateConfig())
                .taskStatus(datasetDO.getTaskStatus())
                .creatorId(datasetDO.getCreatorId())
                .updaterId(datasetDO.getUpdaterId())
                .description(datasetDO.getDescription())
                .addTime(datasetDO.getAddTime())
                .updateTime(datasetDO.getUpdateTime())
                .columnInfoList(columnInfoList)
                .build();
    }
    
    /**
     * 获取列信息数量
     * 
     * @return 列信息数量
     */
    public int getColumnCount() {
        return columnInfoList != null ? columnInfoList.size() : 0;
    }
    
    /**
     * 检查是否包含指定名称的列
     * 
     * @param columnName 列名
     * @return 是否包含该列
     */
    public boolean hasColumn(String columnName) {
        if (columnInfoList == null || columnName == null) {
            return false;
        }
        
        return columnInfoList.stream()
                .anyMatch(column -> columnName.equals(column.getName()));
    }
    
    /**
     * 根据列名获取列信息
     *
     * @param columnName 列名
     * @return 列信息，如不存在则返回null
     */
    public DatasetColumnInfoDO getColumnByName(String columnName) {
        if (columnInfoList == null || columnName == null) {
            return null;
        }

        return columnInfoList.stream()
                .filter(column -> columnName.equals(column.getName()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取数据集ID（兼容性方法）
     *
     * @return 数据集ID
     */
    public Long getDatasetId() {
        return this.id;
    }

    /**
     * 获取业务场景
     *
     * @return 业务场景，如果生成配置为空则返回null
     */
    public String getBusinessScene() {
        return generateConfig != null ? generateConfig.getBusinessScene() : null;
    }

    /**
     * 获取粒度（兼容性方法）
     *
     * @return 数据粒度枚举
     */
    public DataGranularityEnum getGranularity() {
        return this.dataGranularity;
    }


    /**
     * 设置id
     *
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 设置datasetType
     *
     * @param datasetType datasetType
     */
    public void setDatasetType(DatasetTypeEnum datasetType) {
        this.datasetType = datasetType;
    }

    /**
     * 设置dataGranularity
     *
     * @param dataGranularity dataGranularity
     */
    public void setDataGranularity(DataGranularityEnum dataGranularity) {
        this.dataGranularity = dataGranularity;
    }

    /**
     * 设置name
     *
     * @param name name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 设置generateType
     *
     * @param generateType generateType
     */
    public void setGenerateType(GenerateTypeEnum generateType) {
        this.generateType = generateType;
    }

    /**
     * 设置generateConfig
     *
     * @param generateConfig generateConfig
     */
    public void setGenerateConfig(DatasetGenerateConfig generateConfig) {
        this.generateConfig = generateConfig;
    }

    /**
     * 设置taskStatus
     *
     * @param taskStatus taskStatus
     */
    public void setTaskStatus(TaskStatusEnum taskStatus) {
        this.taskStatus = taskStatus;
    }

    /**
     * 设置creatorId
     *
     * @param creatorId creatorId
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 设置updaterId
     *
     * @param updaterId updaterId
     */
    public void setUpdaterId(Long updaterId) {
        this.updaterId = updaterId;
    }

    /**
     * 设置description
     *
     * @param description description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 设置addTime
     *
     * @param addTime addTime
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * 设置updateTime
     *
     * @param updateTime updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 设置columnInfoList
     *
     * @param columnInfoList columnInfoList
     */
    public void setColumnInfoList(List<DatasetColumnInfoDO> columnInfoList) {
        this.columnInfoList = columnInfoList;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static DatasetWDOBuilder builder() {
        return new DatasetWDOBuilder();
    }
    
    /**
     * Builder类
     */
    public static class DatasetWDOBuilder {
        private Long id;
        private DatasetTypeEnum datasetType;
        private DataGranularityEnum dataGranularity;
        private String name;
        private GenerateTypeEnum generateType;
        private DatasetGenerateConfig generateConfig;
        private TaskStatusEnum taskStatus;
        private Long creatorId;
        private Long updaterId;
        private String description;
        private Date addTime;
        private Date updateTime;
        private List<DatasetColumnInfoDO> columnInfoList;
        
        public DatasetWDOBuilder id(Long id) {
            this.id = id;
            return this;
        }
        public DatasetWDOBuilder datasetType(DatasetTypeEnum datasetType) {
            this.datasetType = datasetType;
            return this;
        }
        public DatasetWDOBuilder dataGranularity(DataGranularityEnum dataGranularity) {
            this.dataGranularity = dataGranularity;
            return this;
        }
        public DatasetWDOBuilder name(String name) {
            this.name = name;
            return this;
        }
        public DatasetWDOBuilder generateType(GenerateTypeEnum generateType) {
            this.generateType = generateType;
            return this;
        }
        public DatasetWDOBuilder generateConfig(DatasetGenerateConfig generateConfig) {
            this.generateConfig = generateConfig;
            return this;
        }
        public DatasetWDOBuilder taskStatus(TaskStatusEnum taskStatus) {
            this.taskStatus = taskStatus;
            return this;
        }
        public DatasetWDOBuilder creatorId(Long creatorId) {
            this.creatorId = creatorId;
            return this;
        }
        public DatasetWDOBuilder updaterId(Long updaterId) {
            this.updaterId = updaterId;
            return this;
        }
        public DatasetWDOBuilder description(String description) {
            this.description = description;
            return this;
        }
        public DatasetWDOBuilder addTime(Date addTime) {
            this.addTime = addTime;
            return this;
        }
        public DatasetWDOBuilder updateTime(Date updateTime) {
            this.updateTime = updateTime;
            return this;
        }
        public DatasetWDOBuilder columnInfoList(List<DatasetColumnInfoDO> columnInfoList) {
            this.columnInfoList = columnInfoList;
            return this;
        }
        
        public DatasetWDO build() {
            return new DatasetWDO(id, datasetType, dataGranularity, name, generateType, generateConfig, taskStatus, creatorId, updaterId, description, addTime, updateTime, columnInfoList);
        }
    }
}
