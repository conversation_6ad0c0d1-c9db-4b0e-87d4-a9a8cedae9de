package com.sankuai.dzusergrowth.operation.domain.repository;

import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationTaskDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.AnnotationTaskQuery;

import java.util.List;

/**
 * 标注任务仓储接口
 */
public interface AnnotationTaskRepository {
    
    /**
     * 创建新的标注任务
     * 
     * @param annotationTaskDO 标注任务信息DO
     */
    void createAnnotationTask(AnnotationTaskDO annotationTaskDO);
    
    /**
     * 根据ID获取标注任务
     * 
     * @param taskId 任务ID
     * @return 标注任务信息，如不存在则返回null
     */
    AnnotationTaskDO getAnnotationTaskById(Long taskId);
    
    /**
     * 更新标注任务
     * 
     * @param annotationTaskDO 标注任务DO
     */
    void updateAnnotationTask(AnnotationTaskDO annotationTaskDO);
    
    /**
     * 删除标注任务
     * 
     * @param taskId 任务ID
     */
    void deleteAnnotationTask(Long taskId);
    
    /**
     * 批量查询标注任务
     * 
     * @param query 标注任务查询条件
     * @return 标注任务DO列表
     */
    List<AnnotationTaskDO> queryAnnotationTask(AnnotationTaskQuery query);
} 