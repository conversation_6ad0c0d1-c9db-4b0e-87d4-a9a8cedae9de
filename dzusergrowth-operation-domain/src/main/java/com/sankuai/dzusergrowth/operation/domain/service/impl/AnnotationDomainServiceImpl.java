package com.sankuai.dzusergrowth.operation.domain.service.impl;

import com.sankuai.dzusergrowth.operation.api.enums.ColumnTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.DataTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.TaskStatusEnum;
import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationConfigDO;
import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationTaskDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetColumnConfig;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetColumnInfoDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetWDO;
import com.sankuai.dzusergrowth.operation.domain.model.request.AnnotationTaskCreateRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.AnnotationTaskDeleteRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.AnnotationTaskUpdateRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.DatasetColumnsBatchOperationRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.AnnotationDataItemBatchDeleteRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.DatasetColumnsBatchDeleteRequest;
import com.sankuai.dzusergrowth.operation.domain.repository.AnnotationTaskRepository;
import com.sankuai.dzusergrowth.operation.domain.repository.DatasetColumnInfoRepository;
import com.sankuai.dzusergrowth.operation.domain.repository.AnnotationDataItemRepository;
import com.sankuai.dzusergrowth.operation.domain.service.AnnotationDomainService;
import com.sankuai.dzusergrowth.operation.domain.service.DatasetDomainService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.HashMap;
import java.util.Date;

/**
 * 标注域服务实现
 * 
 * <AUTHOR>
 * @date 2025/7/1 10:37
 */
@Service
public class AnnotationDomainServiceImpl implements AnnotationDomainService {

    @Resource
    private AnnotationTaskRepository annotationTaskRepository;

    @Resource
    private DatasetDomainService datasetDomainService;
    
    @Resource
    private DatasetColumnInfoRepository datasetColumnInfoRepository;

    @Resource
    private AnnotationDataItemRepository annotationDataItemRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AnnotationTaskDO createAnnotationTask(AnnotationTaskCreateRequest request) {
        // 1. 校验请求参数
        validateCreateAnnotationTaskRequest(request);
        
        // 2. 校验数据集聚合根存在并且状态成功
        DatasetWDO datasetWDO = validateDatasetAggregateExistsAndSuccess(request.getAnnotationDatasetId());
        
        // 3. 校验展示列存在
        validateShowColumnsExist(datasetWDO, request.getShowColumn());
        
        // 4. 校验标注配置和列展示名不重复
        validateAnnotationConfigAndDisplayNames(request.getAnnotationConfig(), datasetWDO.getColumnInfoList());
        
        // 5. 校验标注配置并处理列创建（使用统一的processAnnotationConfigChanges方法）
        List<AnnotationConfigDO> completedAnnotationConfigs = processAnnotationConfigChanges(
                request.getAnnotationDatasetId(),
                new ArrayList<>(),  // 创建时原有配置为空
                request.getAnnotationConfig(),
                request.getCreatorId()
        );
        
        // 6. 构建并保存标注任务
        AnnotationTaskDO annotationTaskDO = buildAnnotationTaskDOWithCompletedConfig(request, completedAnnotationConfigs);
        annotationTaskRepository.createAnnotationTask(annotationTaskDO);
        return annotationTaskDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AnnotationTaskDO updateAnnotationTask(AnnotationTaskUpdateRequest request) {
        // 1. 参数校验
        validateUpdateAnnotationTaskRequest(request);
        
        // 2. 获取原有标注任务
        AnnotationTaskDO originalTask = annotationTaskRepository.getAnnotationTaskById(request.getTaskId());
        if (originalTask == null) {
            throw new IllegalStateException("标注任务不存在，任务ID: " + request.getTaskId());
        }
        
        // 3. 校验不可修改字段（数据集ID、标注类型不允许修改）
        validateImmutableFields(request, originalTask);
        
        // 4. 获取数据集聚合根并校验
        DatasetWDO datasetWDO = validateDatasetAggregateExistsAndSuccess(originalTask.getAnnotationDatasetId());
        
        // 5. 校验展示列是否都在数据集中存在（如果有更新展示列）
        validateShowColumnsExist(datasetWDO, request.getShowColumn());
        
        // 6. 处理标注配置的变更（如果有更新标注配置）
        List<AnnotationConfigDO> finalAnnotationConfigs = originalTask.getAnnotationConfig();
        if (request.getAnnotationConfig() != null) {
            // 校验新配置与数据集列展示名不重复（排除已有的）
            validateAnnotationConfigDisplayNames(request.getAnnotationConfig(), datasetWDO.getColumnInfoList(), originalTask.getAnnotationConfig());
            
            // 处理标注配置变更并同步数据集列
            finalAnnotationConfigs = processAnnotationConfigChanges(
                    originalTask.getAnnotationDatasetId(), 
                    originalTask.getAnnotationConfig(), 
                    request.getAnnotationConfig(),
                    request.getUpdaterId());
        }
        
        // 7. 构建更新后的标注任务DO
        AnnotationTaskDO updatedTask = buildUpdatedAnnotationTaskDO(originalTask, request, finalAnnotationConfigs);
        
        // 8. 更新标注任务
        annotationTaskRepository.updateAnnotationTask(updatedTask);
        
        return updatedTask;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAnnotationTask(AnnotationTaskDeleteRequest request) {
        // 1. 参数校验
        validateDeleteAnnotationTaskRequest(request);
        
        // 2. 获取任务并校验存在性
        AnnotationTaskDO task = annotationTaskRepository.getAnnotationTaskById(request.getTaskId());
        if (task == null) {
            throw new IllegalStateException("标注任务不存在，任务ID: " + request.getTaskId());
        }
        
        // 3. 级联删除相关数据
        cascadeDeleteRelatedData(task.getAnnotationDatasetId(), request.getTaskId(), task.getAnnotationConfig(), request.getOperatorId());
        
        // 4. 删除标注任务本身
        annotationTaskRepository.deleteAnnotationTask(request.getTaskId());
    }

    /**
     * 校验创建标注任务请求参数
     */
    private void validateCreateAnnotationTaskRequest(AnnotationTaskCreateRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("创建标注任务请求不能为空");
        }
        
        if (request.getAnnotationDatasetId() == null) {
            throw new IllegalArgumentException("标注数据集ID不能为空");
        }
        
        if (request.getTaskName() == null || request.getTaskName().trim().isEmpty()) {
            throw new IllegalArgumentException("任务名称不能为空");
        }
        
        if (request.getAnnotationType() == null) {
            throw new IllegalArgumentException("标注类型不能为空");
        }
        
        if (request.getCreatorId() == null) {
            throw new IllegalArgumentException("创建人ID不能为空");
        }
        
        if (CollectionUtils.isEmpty(request.getAnnotationConfig())) {
            throw new IllegalArgumentException("标注配置不能为空");
        }
        
        // 验证标注配置的有效性
        for (AnnotationConfigDO config : request.getAnnotationConfig()) {
            if (config == null) {
                throw new IllegalArgumentException("标注配置项不能为空");
            }
            
            if (config.getLabelType() == null) {
                throw new IllegalArgumentException("标签类型不能为空");
            }
            
            if (config.getDisplayName() == null || config.getDisplayName().trim().isEmpty()) {
                throw new IllegalArgumentException("列展示名不能为空");
            }
            
            if (config.getIsRequired() == null) {
                throw new IllegalArgumentException("是否必选不能为空");
            }
            
            if (config.getIsMultiSelected() == null) {
                throw new IllegalArgumentException("是否多选不能为空");
            }
        }
    }

    /**
     * 获取数据集聚合根并校验是否存在且状态为成功
     */
    private DatasetWDO validateDatasetAggregateExistsAndSuccess(Long datasetId) {
        DatasetWDO datasetWDO = datasetDomainService.getDatasetAggregateById(datasetId);
        if (datasetWDO == null) {
            throw new IllegalStateException("数据集不存在，ID: " + datasetId);
        }
        
        if (datasetWDO.getTaskStatus() == null || !datasetWDO.getTaskStatus().isSuccess()) {
            String statusDesc = datasetWDO.getTaskStatus() != null ? 
                    datasetWDO.getTaskStatus().getDescription() : "未知状态";
            throw new IllegalStateException("数据集状态不是成功状态，当前状态: " + statusDesc);
        }
        
        return datasetWDO;
    }

    /**
     * 校验展示列是否都在数据集中存在
     */
    private void validateShowColumnsExist(DatasetWDO datasetWDO, List<Long> showColumnIds) {
        if (CollectionUtils.isEmpty(showColumnIds)) {
            throw new IllegalArgumentException("展示列不能为空");
        }
        
        List<DatasetColumnInfoDO> columnInfoList = datasetWDO.getColumnInfoList();
        if (CollectionUtils.isEmpty(columnInfoList)) {
            throw new IllegalStateException("数据集中没有任何列信息，数据集ID: " + datasetWDO.getId());
        }
        
        // 获取数据集中存在的列ID集合
        Set<Long> existingColumnIds = columnInfoList.stream()
                .map(DatasetColumnInfoDO::getId)
                .collect(Collectors.toSet());
        
        // 检查展示列是否都存在
        for (Long showColumnId : showColumnIds) {
            if (!existingColumnIds.contains(showColumnId)) {
                throw new IllegalStateException("展示列不存在于数据集中，列ID: " + showColumnId + 
                        "，数据集ID: " + datasetWDO.getId());
            }
        }
    }

    /**
     * 校验标注配置和列展示名不重复
     */
    private void validateAnnotationConfigAndDisplayNames(List<AnnotationConfigDO> annotationConfigs, 
                                                       List<DatasetColumnInfoDO> datasetColumns) {
        if (CollectionUtils.isEmpty(annotationConfigs)) {
            throw new IllegalArgumentException("标注配置不能为空");
        }
        
        // 获取数据集中已有的列展示名
        Set<String> existingDisplayNames = datasetColumns.stream()
                .map(DatasetColumnInfoDO::getDisplayName)
                .filter(displayName -> displayName != null && !displayName.trim().isEmpty())
                .collect(Collectors.toSet());
        
        // 检查标注配置中的展示名是否重复
        for (AnnotationConfigDO config : annotationConfigs) {
            if (config.getDisplayName() == null || config.getDisplayName().trim().isEmpty()) {
                throw new IllegalArgumentException("标注配置的列展示名不能为空");
            }
            
            String displayName = config.getDisplayName().trim();
            if (existingDisplayNames.contains(displayName)) {
                throw new IllegalStateException("列展示名已存在: " + displayName);
            }
        }
        
        // 检查标注配置内部展示名是否重复
        Set<String> configDisplayNames = annotationConfigs.stream()
                .map(config -> config.getDisplayName().trim())
                .collect(Collectors.toSet());
        
        if (configDisplayNames.size() != annotationConfigs.size()) {
            throw new IllegalArgumentException("标注配置中存在重复的列展示名");
        }
    }

    /**
     * 用完整的标注配置构建标注任务DO对象
     */
    private AnnotationTaskDO buildAnnotationTaskDOWithCompletedConfig(AnnotationTaskCreateRequest request, 
                                                                      List<AnnotationConfigDO> completedAnnotationConfigs) {
        return AnnotationTaskDO.builder()
                .annotationDatasetId(request.getAnnotationDatasetId())
                .taskName(request.getTaskName())
                .annotationTaskType(request.getAnnotationType())
                .annotationConfig(completedAnnotationConfigs)  // 使用完整的配置
                .showColumn(request.getShowColumn())
                .annotator(request.getAnnotator())
                .description(request.getDescription())
                .creatorId(request.getCreatorId())
                .updaterId(request.getCreatorId())
                .build();
    }

    /**
     * 生成列名（基于展示名）
     */
    private String generateColumnName(String displayName) {
        // 简单的命名规则：将展示名转换为列名
        // 可以根据实际需求调整命名规则
        return "annotation_" + displayName.toLowerCase().replaceAll("[^a-zA-Z0-9_]", "_");
    }

    /**
     * 根据标注配置确定数据类型
     */
    private DataTypeEnum determineDataType(AnnotationConfigDO config) {
        // 根据标注标签类型确定数据类型
        // 这里简化处理，实际可以根据labelType做更精确的判断
        if (config.getLabelType() != null) {
            switch (config.getLabelType()) {
                case TEXT:
                case ENUM:
                    return DataTypeEnum.STRING;
                default:
                    return DataTypeEnum.STRING;
            }
        }
        return DataTypeEnum.STRING;
    }

    /**
     * 构建列配置对象
     */
    private DatasetColumnConfig buildColumnConfig(AnnotationConfigDO config) {
        // 根据标注配置构建列配置
        return DatasetColumnConfig.builder()
                .labelType(config.getLabelType())
                .labelOption(config.getLabelOption())
                .labelIsMultiSelected(config.getIsMultiSelected())
                .labelIsRequired(config.getIsRequired())
                .build();
    }

    /**
     * 校验更新标注任务请求参数
     */
    private void validateUpdateAnnotationTaskRequest(AnnotationTaskUpdateRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("更新标注任务请求不能为空");
        }
        
        if (request.getTaskId() == null) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        if (request.getUpdaterId() == null) {
            throw new IllegalArgumentException("更新人ID不能为空");
        }
        
        // 如果提供了任务名称，需要验证格式
        if (request.getTaskName() != null && request.getTaskName().trim().isEmpty()) {
            throw new IllegalArgumentException("任务名称不能为空字符串");
        }
        
        // 如果提供了标注配置，需要验证有效性
        if (request.getAnnotationConfig() != null) {
            if (request.getAnnotationConfig().isEmpty()) {
                throw new IllegalArgumentException("标注配置不能为空列表");
            }
            
            for (AnnotationConfigDO config : request.getAnnotationConfig()) {
                if (!config.isValid()) {
                    throw new IllegalArgumentException("标注配置无效: " + config.getDisplayName());
                }
            }
        }
        
        // 检查是否有实际要更新的字段
        if (!request.hasFieldsToUpdate()) {
            throw new IllegalArgumentException("没有提供任何要更新的字段");
        }
    }
    
    /**
     * 校验不可修改字段
     */
    private void validateImmutableFields(AnnotationTaskUpdateRequest request, AnnotationTaskDO originalTask) {
        // 校验标注类型不允许修改
        if (request.getAnnotationType() != null && 
            !request.getAnnotationType().equals(originalTask.getAnnotationTaskType())) {
            throw new IllegalArgumentException("标注类型不允许修改，原值: " + 
                originalTask.getAnnotationTaskType() + "，尝试修改为: " + request.getAnnotationType());
        }
    }
    
    /**
     * 校验标注配置展示名不重复（排除已有的配置项）
     */
    private void validateAnnotationConfigDisplayNames(List<AnnotationConfigDO> newAnnotationConfigs, 
                                                     List<DatasetColumnInfoDO> datasetColumns,
                                                     List<AnnotationConfigDO> originalConfigs) {
        if (CollectionUtils.isEmpty(newAnnotationConfigs)) {
            return;
        }
        
        // 获取原有配置的columnId集合
        Set<Long> originalColumnIds = originalConfigs.stream()
                .map(AnnotationConfigDO::getColumnId)
                .filter(columnId -> columnId != null)
                .collect(Collectors.toSet());
        
        // 求datasetColumns与originalConfigs的差集，即真正不受影响的数据集列的展示名
        Set<String> unaffectedColumnDisplayNames = datasetColumns.stream()
                .filter(column -> !originalColumnIds.contains(column.getId()))
                .map(DatasetColumnInfoDO::getDisplayName)
                .filter(displayName -> displayName != null && !displayName.trim().isEmpty())
                .collect(Collectors.toSet());
        
        // 遍历新标注配置，逐一检查展示名重复情况
        for (AnnotationConfigDO config : newAnnotationConfigs) {
            String displayName = config.getDisplayName().trim();
            
            // 检查是否与数据集中不受影响的列展示名重复
            if (unaffectedColumnDisplayNames.contains(displayName)) {
                throw new IllegalStateException("列展示名已存在: " + displayName);
            }
            
            // 记录已处理的展示名
            unaffectedColumnDisplayNames.add(displayName);
        }
    }
    
    /**
     * 处理标注配置变更并同步数据集列（统一的配置处理方法，用于创建和更新）
     */
    private List<AnnotationConfigDO> processAnnotationConfigChanges(Long datasetId,
                                                                   List<AnnotationConfigDO> originalConfigs,
                                                                   List<AnnotationConfigDO> newConfigs,
                                                                   Long operatorId) {
        // 构建原有配置的映射（columnId -> config）
        Map<Long, AnnotationConfigDO> originalConfigMap = originalConfigs.stream()
                .filter(config -> config.getColumnId() != null)
                .collect(Collectors.toMap(AnnotationConfigDO::getColumnId, config -> config));
        
        // 构建新配置的映射（columnId -> config，过滤掉需要新建的）
        Map<Long, AnnotationConfigDO> newConfigMap = newConfigs.stream()
                .filter(config -> config.getColumnId() != null)
                .collect(Collectors.toMap(AnnotationConfigDO::getColumnId, config -> config));
        
        // 1. 识别需要删除的列
        Set<Long> columnsToDelete = originalConfigMap.keySet().stream()
                .filter(columnId -> !newConfigMap.containsKey(columnId))
                .collect(Collectors.toSet());
        
        // 2. 执行删除操作
        if (!columnsToDelete.isEmpty()) {
            DatasetColumnsBatchDeleteRequest deleteRequest = DatasetColumnsBatchDeleteRequest.builder()
                    .datasetId(datasetId)
                    .columnIds(new ArrayList<>(columnsToDelete))
                    .operatorId(operatorId)
                    .build();
            datasetDomainService.batchDeleteDatasetColumns(deleteRequest);
        }
        
        // 3. 构建需要更新/创建的列信息
        List<DatasetColumnInfoDO> columnsToOperate = new ArrayList<>();
        
        // 处理更新的列
        for (AnnotationConfigDO newConfig : newConfigs) {
            if (newConfig.getColumnId() != null) {
                // 校验labelType不允许修改
                AnnotationConfigDO originalConfig = originalConfigMap.get(newConfig.getColumnId());
                if (originalConfig != null && !originalConfig.getLabelType().equals(newConfig.getLabelType())) {
                    throw new IllegalArgumentException("配置项的标签类型不允许修改，列ID: " + newConfig.getColumnId());
                }
                
                // 获取原有列信息用于更新
                DatasetColumnInfoDO originalColumn = datasetColumnInfoRepository.getDatasetColumnInfoById(newConfig.getColumnId());
                if (originalColumn == null) {
                    throw new IllegalStateException("数据集列不存在，列ID: " + newConfig.getColumnId());
                }
                
                // 构建更新后的列信息
                DatasetColumnInfoDO updatedColumn = DatasetColumnInfoDO.builder()
                        .id(originalColumn.getId())
                        .datasetId(datasetId)
                        .name(originalColumn.getName())
                        .displayName(newConfig.getDisplayName())
                        .dataType(originalColumn.getDataType())
                        .columnType(originalColumn.getColumnType())
                        .columnConfig(buildColumnConfig(newConfig))
                        .creatorId(originalColumn.getCreatorId())
                        .updaterId(operatorId)
                        .addTime(originalColumn.getAddTime())
                        .updateTime(new Date())
                        .build();
                
                columnsToOperate.add(updatedColumn);
            } else {
                // 构建新建的列信息
                DatasetColumnInfoDO newColumn = DatasetColumnInfoDO.builder()
                        .datasetId(datasetId)
                        .name(generateColumnName(newConfig.getDisplayName()))
                        .displayName(newConfig.getDisplayName())
                        .dataType(determineDataType(newConfig))
                        .columnType(ColumnTypeEnum.ANNOTATION_RESULT)
                        .columnConfig(buildColumnConfig(newConfig))
                        .creatorId(operatorId)
                        .updaterId(operatorId)
                        .build();
                
                columnsToOperate.add(newColumn);
            }
        }
        
        // 4. 执行批量操作并获取返回的列信息
        List<DatasetColumnInfoDO> resultColumns = new ArrayList<>();
        if (!columnsToOperate.isEmpty()) {
            DatasetColumnsBatchOperationRequest operationRequest = DatasetColumnsBatchOperationRequest.builder()
                    .datasetId(datasetId)
                    .columns(columnsToOperate)
                    .operatorId(operatorId)
                    .build();
            
            resultColumns = datasetDomainService.batchOperateDatasetColumns(operationRequest);
        }
        
        // 5. 基于返回的列信息构建最终的标注配置列表
        List<AnnotationConfigDO> finalConfigs = new ArrayList<>();
        for (int i = 0; i < newConfigs.size(); i++) {
            AnnotationConfigDO newConfig = newConfigs.get(i);
            DatasetColumnInfoDO resultColumn = resultColumns.get(i);
            
            AnnotationConfigDO finalConfig = AnnotationConfigDO.builder()
                    .columnId(resultColumn.getId())
                    .labelType(newConfig.getLabelType())
                    .labelOption(newConfig.getLabelOption())
                    .isMultiSelected(newConfig.getIsMultiSelected())
                    .isRequired(newConfig.getIsRequired())
                    .displayName(newConfig.getDisplayName())
                    .build();
            
            finalConfigs.add(finalConfig);
        }
        
        return finalConfigs;
    }
    

    
    /**
     * 构建更新后的标注任务DO
     */
    private AnnotationTaskDO buildUpdatedAnnotationTaskDO(AnnotationTaskDO originalTask, 
                                                         AnnotationTaskUpdateRequest request,
                                                         List<AnnotationConfigDO> finalAnnotationConfigs) {
        return AnnotationTaskDO.builder()
                .id(originalTask.getId())
                .annotationDatasetId(originalTask.getAnnotationDatasetId())  // 不允许修改
                .taskName(request.getTaskName() != null ? request.getTaskName() : originalTask.getTaskName())
                .annotationTaskType(originalTask.getAnnotationTaskType())  // 不允许修改
                .annotationConfig(finalAnnotationConfigs)
                .showColumn(request.getShowColumn() != null ? request.getShowColumn() : originalTask.getShowColumn())
                .annotator(request.getAnnotator() != null ? request.getAnnotator() : originalTask.getAnnotator())
                .description(request.getDescription() != null ? request.getDescription() : originalTask.getDescription())
                .creatorId(originalTask.getCreatorId())  // 创建人不变
                .updaterId(request.getUpdaterId())  // 更新人
                .addTime(originalTask.getAddTime())  // 创建时间不变
                .updateTime(new Date())  // 更新时间为当前时间
                .build();
    }

    /**
     * 校验删除标注任务请求参数
     */
    private void validateDeleteAnnotationTaskRequest(AnnotationTaskDeleteRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("删除标注任务请求不能为空");
        }
        
        if (!request.isValid()) {
            throw new IllegalArgumentException("删除标注任务请求参数无效：任务ID和操作人ID不能为空");
        }
    }

    /**
     * 级联删除相关数据
     */
    private void cascadeDeleteRelatedData(Long datasetId, Long taskId, List<AnnotationConfigDO> annotationConfigs, Long operatorId) {
        // 删除标注数据项
        deleteAnnotationDataItemsByTaskId(datasetId, taskId);
        
        // 删除标注任务动态创建的数据集列
        deleteAnnotationColumns(datasetId, annotationConfigs, operatorId);
    }

    /**
     * 删除指定任务的所有标注数据项
     */
    private void deleteAnnotationDataItemsByTaskId(Long datasetId, Long taskId) {
        // 构建批量删除请求
        AnnotationDataItemBatchDeleteRequest deleteRequest = AnnotationDataItemBatchDeleteRequest.builder()
                .datasetId(datasetId)
                .taskId(taskId)
                .build();
        
        // 直接批量删除，无需查询
        int deletedCount = annotationDataItemRepository.batchDeleteByDatasetIdAndTaskId(deleteRequest);
        
        // 可选：记录删除的数量日志
        if (deletedCount > 0) {
            // 这里可以添加日志记录
            System.out.println("批量删除标注数据项 " + deletedCount + " 条，数据集ID: " + datasetId + "，任务ID: " + taskId);
        }
    }

    /**
     * 删除标注任务动态创建的数据集列
     */
    private void deleteAnnotationColumns(Long datasetId, List<AnnotationConfigDO> annotationConfigs, Long operatorId) {
        if (CollectionUtils.isEmpty(annotationConfigs)) {
            return;
        }
        
        // 收集所有需要删除的列ID
        List<Long> columnIdsToDelete = annotationConfigs.stream()
                .map(AnnotationConfigDO::getColumnId)
                .filter(java.util.Objects::nonNull)
                .collect(Collectors.toList());
        
        if (columnIdsToDelete.isEmpty()) {
            return;
        }
        
        // 使用批量删除
        DatasetColumnsBatchDeleteRequest deleteRequest = DatasetColumnsBatchDeleteRequest.builder()
                .datasetId(datasetId)
                .columnIds(columnIdsToDelete)
                .operatorId(operatorId)
                .build();
                
        datasetDomainService.batchDeleteDatasetColumns(deleteRequest);
    }
}
