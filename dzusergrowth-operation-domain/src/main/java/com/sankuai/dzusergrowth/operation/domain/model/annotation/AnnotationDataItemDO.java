package com.sankuai.dzusergrowth.operation.domain.model.annotation;

import com.sankuai.dzusergrowth.operation.api.enums.AnnotationDataItemStatusEnum;
import lombok.*;

import java.util.Date;

/**
 * 标注数据条目领域对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationDataItemDO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 数据集ID
     */
    private Long datasetId;
    
    /**
     * 数据唯一键-标识: 比如TraceID，活动ID等
     */
    private String dataUniqueKey;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 状态
     */
    private AnnotationDataItemStatusEnum status;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 添加时间
     */
    private Date addTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 验证数据唯一键不为空
     */
    public boolean isValidDataUniqueKey() {
        return dataUniqueKey != null && !dataUniqueKey.trim().isEmpty();
    }
    
    /**
     * 验证状态是否有效
     */
    public boolean isValidStatus() {
        return status != null;
    }
    
    /**
     * 检查是否为未标注状态
     */
    public boolean isUnAnnotated() {
        return AnnotationDataItemStatusEnum.UN_ANNOTATED.equals(status);
    }
    
    /**
     * 检查是否为标注中状态
     */
    public boolean isAnnotating() {
        return AnnotationDataItemStatusEnum.ANNOTATING.equals(status);
    }
    
    /**
     * 检查是否为已标注状态
     */
    public boolean isAnnotated() {
        return AnnotationDataItemStatusEnum.ANNOTATED.equals(status);
    }


    /**
     * 获取id
     *
     * @return id
     */
    public Long getId() {
        return id;
    }

    /**
     * 获取datasetId
     *
     * @return datasetId
     */
    public Long getDatasetId() {
        return datasetId;
    }

    /**
     * 获取dataUniqueKey
     *
     * @return dataUniqueKey
     */
    public String getDataUniqueKey() {
        return dataUniqueKey;
    }

    /**
     * 获取taskId
     *
     * @return taskId
     */
    public Long getTaskId() {
        return taskId;
    }

    /**
     * 获取status
     *
     * @return status
     */
    public AnnotationDataItemStatusEnum getStatus() {
        return status;
    }

    /**
     * 获取creatorId
     *
     * @return creatorId
     */
    public Long getCreatorId() {
        return creatorId;
    }

    /**
     * 获取updaterId
     *
     * @return updaterId
     */
    public Long getUpdaterId() {
        return updaterId;
    }

    /**
     * 获取addTime
     *
     * @return addTime
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * 获取updateTime
     *
     * @return updateTime
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置id
     *
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 设置datasetId
     *
     * @param datasetId datasetId
     */
    public void setDatasetId(Long datasetId) {
        this.datasetId = datasetId;
    }

    /**
     * 设置dataUniqueKey
     *
     * @param dataUniqueKey dataUniqueKey
     */
    public void setDataUniqueKey(String dataUniqueKey) {
        this.dataUniqueKey = dataUniqueKey;
    }

    /**
     * 设置taskId
     *
     * @param taskId taskId
     */
    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    /**
     * 设置status
     *
     * @param status status
     */
    public void setStatus(AnnotationDataItemStatusEnum status) {
        this.status = status;
    }

    /**
     * 设置creatorId
     *
     * @param creatorId creatorId
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 设置updaterId
     *
     * @param updaterId updaterId
     */
    public void setUpdaterId(Long updaterId) {
        this.updaterId = updaterId;
    }

    /**
     * 设置addTime
     *
     * @param addTime addTime
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * 设置updateTime
     *
     * @param updateTime updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static AnnotationDataItemDOBuilder builder() {
        return new AnnotationDataItemDOBuilder();
    }
    
    /**
     * Builder类
     */
    public static class AnnotationDataItemDOBuilder {
        private Long id;
        private Long datasetId;
        private String dataUniqueKey;
        private Long taskId;
        private AnnotationDataItemStatusEnum status;
        private Long creatorId;
        private Long updaterId;
        private Date addTime;
        private Date updateTime;
        
        public AnnotationDataItemDOBuilder id(Long id) {
            this.id = id;
            return this;
        }
        public AnnotationDataItemDOBuilder datasetId(Long datasetId) {
            this.datasetId = datasetId;
            return this;
        }
        public AnnotationDataItemDOBuilder dataUniqueKey(String dataUniqueKey) {
            this.dataUniqueKey = dataUniqueKey;
            return this;
        }
        public AnnotationDataItemDOBuilder taskId(Long taskId) {
            this.taskId = taskId;
            return this;
        }
        public AnnotationDataItemDOBuilder status(AnnotationDataItemStatusEnum status) {
            this.status = status;
            return this;
        }
        public AnnotationDataItemDOBuilder creatorId(Long creatorId) {
            this.creatorId = creatorId;
            return this;
        }
        public AnnotationDataItemDOBuilder updaterId(Long updaterId) {
            this.updaterId = updaterId;
            return this;
        }
        public AnnotationDataItemDOBuilder addTime(Date addTime) {
            this.addTime = addTime;
            return this;
        }
        public AnnotationDataItemDOBuilder updateTime(Date updateTime) {
            this.updateTime = updateTime;
            return this;
        }
        
        public AnnotationDataItemDO build() {
            return new AnnotationDataItemDO(id, datasetId, dataUniqueKey, taskId, status, creatorId, updaterId, addTime, updateTime);
        }
    }
} 