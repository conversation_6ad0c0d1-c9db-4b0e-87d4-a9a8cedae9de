package com.sankuai.dzusergrowth.operation.domain.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 标注数据条目批量删除请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationDataItemBatchDeleteRequest {
    
    /**
     * 数据集ID
     */
    private Long datasetId;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 验证请求参数有效性
     */
    public boolean isValid() {
        return datasetId != null && taskId != null;
    }
} 