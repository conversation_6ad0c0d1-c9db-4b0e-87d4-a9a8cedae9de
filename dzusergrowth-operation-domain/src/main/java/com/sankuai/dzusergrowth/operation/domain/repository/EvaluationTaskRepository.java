package com.sankuai.dzusergrowth.operation.domain.repository;

import com.sankuai.dzusergrowth.operation.domain.model.evaluation.EvaluationTaskDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.EvaluationTaskQuery;

import java.util.List;

/**
 * 评测任务仓储接口
 */
public interface EvaluationTaskRepository {
    
    /**
     * 创建新的评测任务
     * 
     * @param evaluationTaskDO 评测任务信息DO
     */
    void createEvaluationTask(EvaluationTaskDO evaluationTaskDO);
    
    /**
     * 根据ID获取评测任务
     * 
     * @param taskId 任务ID
     * @return 评测任务信息，如不存在则返回null
     */
    EvaluationTaskDO getEvaluationTaskById(Long taskId);
    
    /**
     * 更新评测任务
     * 
     * @param evaluationTaskDO 评测任务DO
     */
    void updateEvaluationTask(EvaluationTaskDO evaluationTaskDO);
    
    /**
     * 删除评测任务
     * 
     * @param taskId 任务ID
     */
    void deleteEvaluationTask(Long taskId);
    
    /**
     * 批量查询评测任务
     * 
     * @param query 评测任务查询条件
     * @return 评测任务DO列表
     */
    List<EvaluationTaskDO> queryEvaluationTask(EvaluationTaskQuery query);
} 