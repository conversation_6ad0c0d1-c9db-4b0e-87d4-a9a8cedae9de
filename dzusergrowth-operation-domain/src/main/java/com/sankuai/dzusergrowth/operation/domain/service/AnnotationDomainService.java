package com.sankuai.dzusergrowth.operation.domain.service;

import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationTaskDO;
import com.sankuai.dzusergrowth.operation.domain.model.request.AnnotationTaskCreateRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.AnnotationTaskUpdateRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.AnnotationTaskDeleteRequest;

/**
 * 标注域服务
 * 
 * <AUTHOR> Assistant
 */
public interface AnnotationDomainService {
    /**
     * 创建标注任务
     * 
     * 该方法会处理标注配置中columnId为null的情况：
     * 1. 检查标注配置中需要创建新列的项（columnId为null）
     * 2. 为这些项先创建对应的数据集列
     * 3. 获取列ID后更新标注配置
     * 4. 最后创建完整的标注任务
     * 
     * @param request 标注任务创建请求
     * @return 创建后的标注任务DO
     * @throws IllegalArgumentException 当请求参数无效时抛出
     * @throws IllegalStateException 当任务名称已存在或数据集不存在时抛出
     */
    AnnotationTaskDO createAnnotationTask(AnnotationTaskCreateRequest request);
    
    /**
     * 更新标注任务
     * 
     * @param request 标注任务更新请求
     * @return 更新后的标注任务DO
     * @throws IllegalArgumentException 当请求参数无效时抛出
     * @throws IllegalStateException 当任务不存在或无法更新时抛出
     */
    AnnotationTaskDO updateAnnotationTask(AnnotationTaskUpdateRequest request);
    
    /**
     * 删除标注任务
     * 
     * @param request 标注任务删除请求，包含任务ID和操作人ID
     * @throws IllegalArgumentException 当请求参数无效时抛出
     * @throws IllegalStateException 当任务不存在或无法删除时抛出
     */
    void deleteAnnotationTask(AnnotationTaskDeleteRequest request);
}