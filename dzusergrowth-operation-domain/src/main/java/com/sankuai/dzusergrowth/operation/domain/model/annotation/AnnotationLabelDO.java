package com.sankuai.dzusergrowth.operation.domain.model.annotation;

import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 标注标签领域对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationLabelDO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 标签配置（选项列表）
     */
    private List<String> labelConfig;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 添加时间
     */
    private Date addTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 验证标签配置不为空
     */
    public boolean isValidLabelConfig() {
        return labelConfig != null && !labelConfig.isEmpty() && 
               labelConfig.stream().allMatch(config -> config != null && !config.trim().isEmpty());
    }
    
    /**
     * 获取配置选项数量
     */
    public int getConfigOptionCount() {
        return labelConfig != null ? labelConfig.size() : 0;
    }
    
    /**
     * 检查是否包含指定选项
     */
    public boolean containsOption(String option) {
        return labelConfig != null && labelConfig.contains(option);
    }


    /**
     * 设置id
     *
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 设置labelConfig
     *
     * @param labelConfig labelConfig
     */
    public void setLabelConfig(List<String> labelConfig) {
        this.labelConfig = labelConfig;
    }

    /**
     * 设置creatorId
     *
     * @param creatorId creatorId
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 设置updaterId
     *
     * @param updaterId updaterId
     */
    public void setUpdaterId(Long updaterId) {
        this.updaterId = updaterId;
    }

    /**
     * 设置addTime
     *
     * @param addTime addTime
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * 设置updateTime
     *
     * @param updateTime updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static AnnotationLabelDOBuilder builder() {
        return new AnnotationLabelDOBuilder();
    }
    
    /**
     * Builder类
     */
    public static class AnnotationLabelDOBuilder {
        private Long id;
        private List<String> labelConfig;
        private Long creatorId;
        private Long updaterId;
        private Date addTime;
        private Date updateTime;
        
        public AnnotationLabelDOBuilder id(Long id) {
            this.id = id;
            return this;
        }
        public AnnotationLabelDOBuilder labelConfig(List<String> labelConfig) {
            this.labelConfig = labelConfig;
            return this;
        }
        public AnnotationLabelDOBuilder creatorId(Long creatorId) {
            this.creatorId = creatorId;
            return this;
        }
        public AnnotationLabelDOBuilder updaterId(Long updaterId) {
            this.updaterId = updaterId;
            return this;
        }
        public AnnotationLabelDOBuilder addTime(Date addTime) {
            this.addTime = addTime;
            return this;
        }
        public AnnotationLabelDOBuilder updateTime(Date updateTime) {
            this.updateTime = updateTime;
            return this;
        }
        
        public AnnotationLabelDO build() {
            return new AnnotationLabelDO(id, labelConfig, creatorId, updaterId, addTime, updateTime);
        }
    }
} 