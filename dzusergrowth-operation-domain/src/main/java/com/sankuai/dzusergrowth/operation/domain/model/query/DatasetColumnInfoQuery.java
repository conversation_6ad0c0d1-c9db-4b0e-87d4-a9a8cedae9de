package com.sankuai.dzusergrowth.operation.domain.model.query;

import com.sankuai.dzusergrowth.operation.api.enums.DataTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.ColumnTypeEnum;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 数据集列信息查询条件
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetColumnInfoQuery {
    
    /**
     * 列ID列表
     */
    private List<Long> columnIds;
    
    /**
     * 数据集ID
     */
    private Long datasetId;
    
    /**
     * 数据集ID列表
     */
    private List<Long> datasetIds;
    
    /**
     * 列名（模糊查询）
     */
    private String name;
    
    /**
     * 数据类型: 1-string; 2-long; 3-float
     */
    private Integer dataType;
    
    /**
     * 列类型: 1-生成数据; 2-标注结果; 3-统计指标; 4-大模型评测指标
     */
    private Integer columnType;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建时间开始
     */
    private Date addTimeStart;
    
    /**
     * 创建时间结束
     */
    private Date addTimeEnd;
    
    /**
     * 分页页码（从1开始）
     */
    private Integer pageNum;
    
    /**
     * 分页大小
     */
    private Integer pageSize;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向: ASC/DESC
     */
    private String orderDirection;
    
    /**
     * 通过数据类型枚举设置查询条件
     *
     * @param dataTypeEnum 数据类型枚举
     * @return 当前查询对象，支持链式调用
     */
    public DatasetColumnInfoQuery setDataType(DataTypeEnum dataTypeEnum) {
        this.dataType = dataTypeEnum != null ? dataTypeEnum.getCode() : null;
        return this;
    }
    
    /**
     * 通过列类型枚举设置查询条件
     *
     * @param columnTypeEnum 列类型枚举
     * @return 当前查询对象，支持链式调用
     */
    public DatasetColumnInfoQuery setColumnType(ColumnTypeEnum columnTypeEnum) {
        this.columnType = columnTypeEnum != null ? columnTypeEnum.getCode() : null;
        return this;
    }


    /**
     * 获取columnIds
     *
     * @return columnIds
     */
    public List<Long> getColumnIds() {
        return columnIds;
    }

    /**
     * 获取datasetId
     *
     * @return datasetId
     */
    public Long getDatasetId() {
        return datasetId;
    }

    /**
     * 获取datasetIds
     *
     * @return datasetIds
     */
    public List<Long> getDatasetIds() {
        return datasetIds;
    }

    /**
     * 获取name
     *
     * @return name
     */
    public String getName() {
        return name;
    }

    /**
     * 获取dataType
     *
     * @return dataType
     */
    public Integer getDataType() {
        return dataType;
    }

    /**
     * 获取columnType
     *
     * @return columnType
     */
    public Integer getColumnType() {
        return columnType;
    }

    /**
     * 获取creatorId
     *
     * @return creatorId
     */
    public Long getCreatorId() {
        return creatorId;
    }

    /**
     * 获取addTimeStart
     *
     * @return addTimeStart
     */
    public Date getAddTimeStart() {
        return addTimeStart;
    }

    /**
     * 获取addTimeEnd
     *
     * @return addTimeEnd
     */
    public Date getAddTimeEnd() {
        return addTimeEnd;
    }

    /**
     * 获取pageNum
     *
     * @return pageNum
     */
    public Integer getPageNum() {
        return pageNum;
    }

    /**
     * 获取pageSize
     *
     * @return pageSize
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * 获取orderBy
     *
     * @return orderBy
     */
    public String getOrderBy() {
        return orderBy;
    }

    /**
     * 获取orderDirection
     *
     * @return orderDirection
     */
    public String getOrderDirection() {
        return orderDirection;
    }

    /**
     * 设置columnIds
     *
     * @param columnIds columnIds
     */
    public void setColumnIds(List<Long> columnIds) {
        this.columnIds = columnIds;
    }

    /**
     * 设置datasetId
     *
     * @param datasetId datasetId
     */
    public void setDatasetId(Long datasetId) {
        this.datasetId = datasetId;
    }

    /**
     * 设置datasetIds
     *
     * @param datasetIds datasetIds
     */
    public void setDatasetIds(List<Long> datasetIds) {
        this.datasetIds = datasetIds;
    }

    /**
     * 设置name
     *
     * @param name name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 设置dataType
     *
     * @param dataType dataType
     */
    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    /**
     * 设置columnType
     *
     * @param columnType columnType
     */
    public void setColumnType(Integer columnType) {
        this.columnType = columnType;
    }

    /**
     * 设置creatorId
     *
     * @param creatorId creatorId
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 设置addTimeStart
     *
     * @param addTimeStart addTimeStart
     */
    public void setAddTimeStart(Date addTimeStart) {
        this.addTimeStart = addTimeStart;
    }

    /**
     * 设置addTimeEnd
     *
     * @param addTimeEnd addTimeEnd
     */
    public void setAddTimeEnd(Date addTimeEnd) {
        this.addTimeEnd = addTimeEnd;
    }

    /**
     * 设置pageNum
     *
     * @param pageNum pageNum
     */
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    /**
     * 设置pageSize
     *
     * @param pageSize pageSize
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 设置orderBy
     *
     * @param orderBy orderBy
     */
    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    /**
     * 设置orderDirection
     *
     * @param orderDirection orderDirection
     */
    public void setOrderDirection(String orderDirection) {
        this.orderDirection = orderDirection;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static DatasetColumnInfoQueryBuilder builder() {
        return new DatasetColumnInfoQueryBuilder();
    }
    
    /**
     * Builder类
     */
    public static class DatasetColumnInfoQueryBuilder {
        private List<Long> columnIds;
        private Long datasetId;
        private List<Long> datasetIds;
        private String name;
        private Integer dataType;
        private Integer columnType;
        private Long creatorId;
        private Date addTimeStart;
        private Date addTimeEnd;
        private Integer pageNum;
        private Integer pageSize;
        private String orderBy;
        private String orderDirection;
        
        public DatasetColumnInfoQueryBuilder columnIds(List<Long> columnIds) {
            this.columnIds = columnIds;
            return this;
        }
        public DatasetColumnInfoQueryBuilder datasetId(Long datasetId) {
            this.datasetId = datasetId;
            return this;
        }
        public DatasetColumnInfoQueryBuilder datasetIds(List<Long> datasetIds) {
            this.datasetIds = datasetIds;
            return this;
        }
        public DatasetColumnInfoQueryBuilder name(String name) {
            this.name = name;
            return this;
        }
        public DatasetColumnInfoQueryBuilder dataType(Integer dataType) {
            this.dataType = dataType;
            return this;
        }
        public DatasetColumnInfoQueryBuilder columnType(Integer columnType) {
            this.columnType = columnType;
            return this;
        }
        public DatasetColumnInfoQueryBuilder creatorId(Long creatorId) {
            this.creatorId = creatorId;
            return this;
        }
        public DatasetColumnInfoQueryBuilder addTimeStart(Date addTimeStart) {
            this.addTimeStart = addTimeStart;
            return this;
        }
        public DatasetColumnInfoQueryBuilder addTimeEnd(Date addTimeEnd) {
            this.addTimeEnd = addTimeEnd;
            return this;
        }
        public DatasetColumnInfoQueryBuilder pageNum(Integer pageNum) {
            this.pageNum = pageNum;
            return this;
        }
        public DatasetColumnInfoQueryBuilder pageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }
        public DatasetColumnInfoQueryBuilder orderBy(String orderBy) {
            this.orderBy = orderBy;
            return this;
        }
        public DatasetColumnInfoQueryBuilder orderDirection(String orderDirection) {
            this.orderDirection = orderDirection;
            return this;
        }
        
        public DatasetColumnInfoQuery build() {
            return new DatasetColumnInfoQuery(columnIds, datasetId, datasetIds, name, dataType, columnType, creatorId, addTimeStart, addTimeEnd, pageNum, pageSize, orderBy, orderDirection);
        }
    }
} 