package com.sankuai.dzusergrowth.operation.domain.repository;

import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetColumnInfoDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.DatasetColumnInfoQuery;

import java.util.List;

/**
 * 数据集列信息仓储接口
 */
public interface DatasetColumnInfoRepository {
    
    /**
     * 创建新的数据集列信息
     * 
     * @param columnInfoDO 数据集列信息DO
     */
    void createDatasetColumnInfo(DatasetColumnInfoDO columnInfoDO);
    
    /**
     * 根据ID获取数据集列信息
     * 
     * @param columnId 列ID
     * @return 数据集列信息，如不存在则返回null
     */
    DatasetColumnInfoDO getDatasetColumnInfoById(Long columnId);
    
    /**
     * 更新数据集列信息
     * 
     * @param columnInfoDO 数据集列信息DO
     */
    void updateDatasetColumnInfo(DatasetColumnInfoDO columnInfoDO);
    
    /**
     * 删除数据集列信息
     * 
     * @param columnId 列ID
     */
    void deleteDatasetColumnInfo(Long columnId);
    
    /**
     * 批量查询数据集列信息
     * 
     * @param query 数据集列信息查询条件
     * @return 数据集列信息DO列表
     */
    List<DatasetColumnInfoDO> queryDatasetColumnInfo(DatasetColumnInfoQuery query);
    
    /**
     * 检查指定数据集中是否存在同名列
     * 
     * @param datasetId 数据集ID
     * @param columnName 列名
     * @param excludeId 排除的列ID（用于更新时检查重名，可为null）
     * @return 是否存在同名列
     */
    boolean isColumnNameExists(Long datasetId, String columnName, Long excludeId);
} 