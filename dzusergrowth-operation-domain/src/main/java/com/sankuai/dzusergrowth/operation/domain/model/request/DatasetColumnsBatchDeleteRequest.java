package com.sankuai.dzusergrowth.operation.domain.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数据集列批量删除请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetColumnsBatchDeleteRequest {
    
    /**
     * 数据集ID
     */
    private Long datasetId;
    
    /**
     * 要删除的列ID列表
     */
    private List<Long> columnIds;
    
    /**
     * 操作人ID
     */
    private Long operatorId;
    
    /**
     * 验证请求参数有效性
     */
    public boolean isValid() {
        // 基础字段验证
        if (datasetId == null) {
            return false;
        }
        
        if (operatorId == null) {
            return false;
        }
        
        if (columnIds == null || columnIds.isEmpty()) {
            return false;
        }
        
        // 检查列ID列表中是否有null值
        for (Long columnId : columnIds) {
            if (columnId == null) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 验证数据集ID是否有效
     */
    public boolean hasValidDatasetId() {
        return datasetId != null && datasetId > 0;
    }
    
    /**
     * 验证列ID列表是否为空
     */
    public boolean hasColumnIds() {
        return columnIds != null && !columnIds.isEmpty();
    }
    
    /**
     * 获取要删除的列数量
     */
    public int getColumnCount() {
        return columnIds != null ? columnIds.size() : 0;
    }
} 