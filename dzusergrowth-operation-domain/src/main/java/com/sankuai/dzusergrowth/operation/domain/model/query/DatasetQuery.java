package com.sankuai.dzusergrowth.operation.domain.model.query;

import com.sankuai.dzusergrowth.operation.api.enums.DatasetTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.DataGranularityEnum;
import com.sankuai.dzusergrowth.operation.api.enums.GenerateTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.AssetTypeEnum;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 数据集查询条件
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetQuery {
    
    /**
     * 数据集ID列表
     */
    private List<Long> datasetIds;
    
    /**
     * 数据集类型: 1-原始数据集; 2-评测结果集
     */
    private Integer datasetType;
    
    /**
     * 数据粒度: 1-活动; 2-任务; 3-query; 4-log
     */
    private Integer dataGranularity;

    /**
     * 活动类型: 1-活动投放; 2-内容营销
     * 需要从generateConfig的JSON中解析activity_type字段
     */
    private Integer activityType;

    /**
     * 资产类型: 1-笔记; 2-图片; 3-会场
     * 需要从generateConfig的JSON中解析asset_type字段
     */
    private Integer assetType;
    
    /**
     * 数据集名称（模糊查询）
     */
    private String name;
    
    /**
     * 生成方式: 1-条件来源; 2-手动上传; 3-明确来源
     */
    private Integer generateType;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建时间开始
     */
    private Date addTimeStart;
    
    /**
     * 创建时间结束
     */
    private Date addTimeEnd;
    
    /**
     * 分页页码（从1开始）
     */
    private Integer pageNum;
    
    /**
     * 分页大小
     */
    private Integer pageSize;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向: ASC/DESC
     */
    private String orderDirection;
    
    /**
     * 通过数据集类型枚举设置查询条件
     *
     * @param datasetTypeEnum 数据集类型枚举
     * @return 当前查询对象，支持链式调用
     */
    public DatasetQuery setDatasetType(DatasetTypeEnum datasetTypeEnum) {
        this.datasetType = datasetTypeEnum != null ? datasetTypeEnum.getCode() : null;
        return this;
    }
    
    /**
     * 通过数据粒度枚举设置查询条件
     *
     * @param dataGranularityEnum 数据粒度枚举
     * @return 当前查询对象，支持链式调用
     */
    public DatasetQuery setDataGranularity(DataGranularityEnum dataGranularityEnum) {
        this.dataGranularity = dataGranularityEnum != null ? dataGranularityEnum.getCode() : null;
        return this;
    }
    
    /**
     * 通过生成方式枚举设置查询条件
     *
     * @param generateTypeEnum 生成方式枚举
     * @return 当前查询对象，支持链式调用
     */
    public DatasetQuery setGenerateType(GenerateTypeEnum generateTypeEnum) {
        this.generateType = generateTypeEnum != null ? generateTypeEnum.getCode() : null;
        return this;
    }
    
    /**
     * 通过资产类型枚举设置查询条件
     *
     * @param assetTypeEnum 资产类型枚举
     * @return 当前查询对象，支持链式调用
     */
    public DatasetQuery setAssetType(AssetTypeEnum assetTypeEnum) {
        this.assetType = assetTypeEnum != null ? assetTypeEnum.getCode() : null;
        return this;
    }


    /**
     * 获取datasetIds
     *
     * @return datasetIds
     */
    public List<Long> getDatasetIds() {
        return datasetIds;
    }

    /**
     * 获取datasetType
     *
     * @return datasetType
     */
    public Integer getDatasetType() {
        return datasetType;
    }

    /**
     * 获取dataGranularity
     *
     * @return dataGranularity
     */
    public Integer getDataGranularity() {
        return dataGranularity;
    }

    /**
     * 获取activityType
     *
     * @return activityType
     */
    public Integer getActivityType() {
        return activityType;
    }

    /**
     * 获取assetType
     *
     * @return assetType
     */
    public Integer getAssetType() {
        return assetType;
    }

    /**
     * 获取name
     *
     * @return name
     */
    public String getName() {
        return name;
    }

    /**
     * 获取generateType
     *
     * @return generateType
     */
    public Integer getGenerateType() {
        return generateType;
    }

    /**
     * 获取creatorId
     *
     * @return creatorId
     */
    public Long getCreatorId() {
        return creatorId;
    }

    /**
     * 获取addTimeStart
     *
     * @return addTimeStart
     */
    public Date getAddTimeStart() {
        return addTimeStart;
    }

    /**
     * 获取addTimeEnd
     *
     * @return addTimeEnd
     */
    public Date getAddTimeEnd() {
        return addTimeEnd;
    }

    /**
     * 获取pageNum
     *
     * @return pageNum
     */
    public Integer getPageNum() {
        return pageNum;
    }

    /**
     * 获取pageSize
     *
     * @return pageSize
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * 获取orderBy
     *
     * @return orderBy
     */
    public String getOrderBy() {
        return orderBy;
    }

    /**
     * 获取orderDirection
     *
     * @return orderDirection
     */
    public String getOrderDirection() {
        return orderDirection;
    }

    /**
     * 设置datasetIds
     *
     * @param datasetIds datasetIds
     */
    public void setDatasetIds(List<Long> datasetIds) {
        this.datasetIds = datasetIds;
    }

    /**
     * 设置datasetType
     *
     * @param datasetType datasetType
     */
    public void setDatasetType(Integer datasetType) {
        this.datasetType = datasetType;
    }

    /**
     * 设置dataGranularity
     *
     * @param dataGranularity dataGranularity
     */
    public void setDataGranularity(Integer dataGranularity) {
        this.dataGranularity = dataGranularity;
    }

    /**
     * 设置activityType
     *
     * @param activityType activityType
     */
    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }

    /**
     * 设置assetType
     *
     * @param assetType assetType
     */
    public void setAssetType(Integer assetType) {
        this.assetType = assetType;
    }

    /**
     * 设置name
     *
     * @param name name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 设置generateType
     *
     * @param generateType generateType
     */
    public void setGenerateType(Integer generateType) {
        this.generateType = generateType;
    }

    /**
     * 设置creatorId
     *
     * @param creatorId creatorId
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 设置addTimeStart
     *
     * @param addTimeStart addTimeStart
     */
    public void setAddTimeStart(Date addTimeStart) {
        this.addTimeStart = addTimeStart;
    }

    /**
     * 设置addTimeEnd
     *
     * @param addTimeEnd addTimeEnd
     */
    public void setAddTimeEnd(Date addTimeEnd) {
        this.addTimeEnd = addTimeEnd;
    }

    /**
     * 设置pageNum
     *
     * @param pageNum pageNum
     */
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    /**
     * 设置pageSize
     *
     * @param pageSize pageSize
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 设置orderBy
     *
     * @param orderBy orderBy
     */
    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    /**
     * 设置orderDirection
     *
     * @param orderDirection orderDirection
     */
    public void setOrderDirection(String orderDirection) {
        this.orderDirection = orderDirection;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static DatasetQueryBuilder builder() {
        return new DatasetQueryBuilder();
    }
    
    /**
     * Builder类
     */
    public static class DatasetQueryBuilder {
        private List<Long> datasetIds;
        private Integer datasetType;
        private Integer dataGranularity;
        private Integer activityType;
        private Integer assetType;
        private String name;
        private Integer generateType;
        private Long creatorId;
        private Date addTimeStart;
        private Date addTimeEnd;
        private Integer pageNum;
        private Integer pageSize;
        private String orderBy;
        private String orderDirection;
        
        public DatasetQueryBuilder datasetIds(List<Long> datasetIds) {
            this.datasetIds = datasetIds;
            return this;
        }
        public DatasetQueryBuilder datasetType(Integer datasetType) {
            this.datasetType = datasetType;
            return this;
        }
        public DatasetQueryBuilder dataGranularity(Integer dataGranularity) {
            this.dataGranularity = dataGranularity;
            return this;
        }
        public DatasetQueryBuilder activityType(Integer activityType) {
            this.activityType = activityType;
            return this;
        }
        public DatasetQueryBuilder assetType(Integer assetType) {
            this.assetType = assetType;
            return this;
        }
        public DatasetQueryBuilder name(String name) {
            this.name = name;
            return this;
        }
        public DatasetQueryBuilder generateType(Integer generateType) {
            this.generateType = generateType;
            return this;
        }
        public DatasetQueryBuilder creatorId(Long creatorId) {
            this.creatorId = creatorId;
            return this;
        }
        public DatasetQueryBuilder addTimeStart(Date addTimeStart) {
            this.addTimeStart = addTimeStart;
            return this;
        }
        public DatasetQueryBuilder addTimeEnd(Date addTimeEnd) {
            this.addTimeEnd = addTimeEnd;
            return this;
        }
        public DatasetQueryBuilder pageNum(Integer pageNum) {
            this.pageNum = pageNum;
            return this;
        }
        public DatasetQueryBuilder pageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }
        public DatasetQueryBuilder orderBy(String orderBy) {
            this.orderBy = orderBy;
            return this;
        }
        public DatasetQueryBuilder orderDirection(String orderDirection) {
            this.orderDirection = orderDirection;
            return this;
        }
        
        public DatasetQuery build() {
            return new DatasetQuery(datasetIds, datasetType, dataGranularity, activityType, assetType, name, generateType, creatorId, addTimeStart, addTimeEnd, pageNum, pageSize, orderBy, orderDirection);
        }
    }
} 