package com.sankuai.dzusergrowth.operation.domain.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 标注任务删除请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationTaskDeleteRequest {
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 操作人ID
     */
    private Long operatorId;
    
    /**
     * 验证请求参数有效性
     */
    public boolean isValid() {
        return taskId != null && operatorId != null;
    }


    /**
     * 获取taskId
     *
     * @return taskId
     */
    public Long getTaskId() {
        return taskId;
    }

    /**
     * 获取operatorId
     *
     * @return operatorId
     */
    public Long getOperatorId() {
        return operatorId;
    }

    /**
     * 设置taskId
     *
     * @param taskId taskId
     */
    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    /**
     * 设置operatorId
     *
     * @param operatorId operatorId
     */
    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static AnnotationTaskDeleteRequestBuilder builder() {
        return new AnnotationTaskDeleteRequestBuilder();
    }
    
    /**
     * Builder类
     */
    public static class AnnotationTaskDeleteRequestBuilder {
        private Long taskId;
        private Long operatorId;
        
        public AnnotationTaskDeleteRequestBuilder taskId(Long taskId) {
            this.taskId = taskId;
            return this;
        }
        public AnnotationTaskDeleteRequestBuilder operatorId(Long operatorId) {
            this.operatorId = operatorId;
            return this;
        }
        
        public AnnotationTaskDeleteRequest build() {
            return new AnnotationTaskDeleteRequest(taskId, operatorId);
        }
    }
} 