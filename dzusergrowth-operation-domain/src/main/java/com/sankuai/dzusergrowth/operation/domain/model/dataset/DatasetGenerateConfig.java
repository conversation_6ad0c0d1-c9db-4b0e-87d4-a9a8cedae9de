package com.sankuai.dzusergrowth.operation.domain.model.dataset;

import com.sankuai.dzusergrowth.ai.assistant.api.enums.ActivityTypeEnum;
import com.sankuai.dzusergrowth.ai.assistant.api.enums.TaskTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.AssetTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.FixedMetricEnum;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonSetter;
import lombok.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 数据集生成配置对象
 * 对应数据集表中generateConfig字段的JSON结构
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetGenerateConfig {
    
    /**
     * 业务场景标识
     */
    private String businessScene;

    /**
     * 采集开始时间
     */
    private Date startTime;

    /**
     * 采集结束时间
     */
    private Date endTime;

    /**
     * 采集用户ID列表
     */
    private List<String> userId;
    
    /**
     * 活动类型
     */
    @com.fasterxml.jackson.annotation.JsonIgnore
    private ActivityTypeEnum activityType;
    
    /**
     * 任务类型
     */
    @com.fasterxml.jackson.annotation.JsonIgnore
    private TaskTypeEnum taskType;
    
    /**
     * 资产类型
     */
    @com.fasterxml.jackson.annotation.JsonIgnore
    private AssetTypeEnum assetType;
    
    /**
     * 采集固定指标ID列表（字符串ID）
     */
    private List<String> collectIds;

    /**
     * 被采集MIS账号列表
     */
    private List<String> collectedMisIds;

    /**
     * 采样固定指标列表
     */
    private List<String> sampleFixedMetrics;
    
    /**
     * JSON序列化时将活动类型枚举转换为code值
     */
    @JsonGetter("activityType")
    public Integer getActivityTypeForJson() {
        return activityType != null ? activityType.getCode() : null;
    }
    
    /**
     * JSON反序列化时将code值转换为活动类型枚举
     */
    @JsonSetter("activityType")
    public void setActivityTypeFromJson(Integer code) {
        this.activityType = ActivityTypeEnum.getByCode(code);
    }
    
    /**
     * JSON序列化时将任务类型枚举转换为code值
     */
    @JsonGetter("taskType")
    public Integer getTaskTypeForJson() {
        return taskType != null ? taskType.getCode() : null;
    }
    
    /**
     * JSON反序列化时将code值转换为任务类型枚举
     */
    @JsonSetter("taskType")
    public void setTaskTypeFromJson(Integer code) {
        this.taskType = TaskTypeEnum.getByCode(code);
    }
    
    /**
     * JSON序列化时将资产类型枚举转换为code值
     */
    @JsonGetter("assetType")
    public Integer getAssetTypeForJson() {
        return assetType != null ? assetType.getCode() : null;
    }
    
    /**
     * JSON反序列化时将code值转换为资产类型枚举
     */
    @JsonSetter("assetType")
    public void setAssetTypeFromJson(Integer code) {
        this.assetType = AssetTypeEnum.getByCode(code);
    }
    
    /**
     * 验证配置是否有效
     */
    public boolean isValid() {
        return startTime != null && endTime != null && 
               startTime.before(endTime) &&
               (activityType != null || assetType != null);
    }
    
    /**
     * 获取活动类型编码（兼容性方法）
     */
    public Integer getActivityTypeCode() {
        return activityType != null ? activityType.getCode() : null;
    }
    
    /**
     * 获取任务类型编码（兼容性方法）
     */
    public Integer getTaskTypeCode() {
        return taskType != null ? taskType.getCode() : null;
    }
    
    /**
     * 获取资产类型编码（兼容性方法）
     */
    public Integer getAssetTypeCode() {
        return assetType != null ? assetType.getCode() : null;
    }
    
    /**
     * 获取活动类型文本描述
     */
    public String getActivityTypeText() {
        return activityType != null ? activityType.getDesc() : "未知";
    }
    
    /**
     * 获取任务类型文本描述
     */
    public String getTaskTypeText() {
        return taskType != null ? taskType.getDesc() : "未知";
    }
    
    /**
     * 获取资产类型文本描述
     */
    public String getAssetTypeText() {
        return assetType != null ? assetType.getDesc() : "未知";
    }



    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static DatasetGenerateConfigBuilder builder() {
        return new DatasetGenerateConfigBuilder();
    }
    
    /**
     * Builder类
     */
    public static class DatasetGenerateConfigBuilder {
        private String businessScene;
        private Date startTime;
        private Date endTime;
        private List<String> userId;
        private ActivityTypeEnum activityType;
        private TaskTypeEnum taskType;
        private AssetTypeEnum assetType;
        private List<String> collectIds;
        private List<String> collectedMisIds;
        private List<String> sampleFixedMetrics;
        
        public DatasetGenerateConfigBuilder businessScene(String businessScene) {
            this.businessScene = businessScene;
            return this;
        }
        public DatasetGenerateConfigBuilder startTime(Date startTime) {
            this.startTime = startTime;
            return this;
        }
        public DatasetGenerateConfigBuilder endTime(Date endTime) {
            this.endTime = endTime;
            return this;
        }
        public DatasetGenerateConfigBuilder userId(List<String> userId) {
            this.userId = userId;
            return this;
        }
        public DatasetGenerateConfigBuilder activityType(ActivityTypeEnum activityType) {
            this.activityType = activityType;
            return this;
        }
        public DatasetGenerateConfigBuilder taskType(TaskTypeEnum taskType) {
            this.taskType = taskType;
            return this;
        }
        public DatasetGenerateConfigBuilder assetType(AssetTypeEnum assetType) {
            this.assetType = assetType;
            return this;
        }
        public DatasetGenerateConfigBuilder collectIds(List<String> collectIds) {
            this.collectIds = collectIds;
            return this;
        }
        public DatasetGenerateConfigBuilder collectedMisIds(List<String> collectedMisIds) {
            this.collectedMisIds = collectedMisIds;
            return this;
        }
        public DatasetGenerateConfigBuilder sampleFixedMetrics(List<String> sampleFixedMetrics) {
            this.sampleFixedMetrics = sampleFixedMetrics;
            return this;
        }
        
        public DatasetGenerateConfig build() {
            return new DatasetGenerateConfig(businessScene, startTime, endTime, userId, activityType, taskType, assetType, collectIds, collectedMisIds, sampleFixedMetrics);
        }
    }
} 