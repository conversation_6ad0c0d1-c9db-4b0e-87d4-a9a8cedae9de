package com.sankuai.dzusergrowth.operation.domain.model.annotation;

import com.sankuai.dzusergrowth.operation.api.enums.AnnotationLabelTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 标注配置领域对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationConfigDO {
    
    /**
     * 标注列Id (从数据集列信息表中映射得到)
     */
    private Long columnId;
    
    /**
     * 标签类型
     */
    private AnnotationLabelTypeEnum labelType;
    
    /**
     * 标签选项（仅枚举类型有值）
     */
    private List<String> labelOption;
    
    /**
     * 是否多选
     */
    private Boolean isMultiSelected;
    
    /**
     * 是否必选
     */
    private Boolean isRequired;
    
    /**
     * 列展示名
     */
    private String displayName;
    
    /**
     * 检查是否需要创建新的数据集列
     * 
     * @return 如果columnId为null，表示需要创建新列
     */
    public boolean needCreateColumn() {
        return columnId == null;
    }
    
    /**
     * 验证标注配置是否有效（不包括columnId检查）
     * 
     * @return 配置是否有效
     */
    public boolean isValid() {
        return labelType != null && 
               displayName != null && !displayName.trim().isEmpty() &&
               isRequired != null &&
               isMultiSelected != null;
    }


    /**
     * 获取columnId
     *
     * @return columnId
     */
    public Long getColumnId() {
        return columnId;
    }

    /**
     * 获取labelType
     *
     * @return labelType
     */
    public AnnotationLabelTypeEnum getLabelType() {
        return labelType;
    }

    /**
     * 获取labelOption
     *
     * @return labelOption
     */
    public List<String> getLabelOption() {
        return labelOption;
    }

    /**
     * 获取isMultiSelected
     *
     * @return isMultiSelected
     */
    public Boolean isMultiSelected() {
        return isMultiSelected;
    }

    /**
     * 获取isRequired
     *
     * @return isRequired
     */
    public Boolean isRequired() {
        return isRequired;
    }

    /**
     * 获取displayName
     *
     * @return displayName
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 设置columnId
     *
     * @param columnId columnId
     */
    public void setColumnId(Long columnId) {
        this.columnId = columnId;
    }

    /**
     * 设置labelType
     *
     * @param labelType labelType
     */
    public void setLabelType(AnnotationLabelTypeEnum labelType) {
        this.labelType = labelType;
    }

    /**
     * 设置labelOption
     *
     * @param labelOption labelOption
     */
    public void setLabelOption(List<String> labelOption) {
        this.labelOption = labelOption;
    }

    /**
     * 设置isMultiSelected
     *
     * @param isMultiSelected isMultiSelected
     */
    public void setIsMultiSelected(Boolean isMultiSelected) {
        this.isMultiSelected = isMultiSelected;
    }

    /**
     * 设置isRequired
     *
     * @param isRequired isRequired
     */
    public void setIsRequired(Boolean isRequired) {
        this.isRequired = isRequired;
    }

    /**
     * 设置displayName
     *
     * @param displayName displayName
     */
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static AnnotationConfigDOBuilder builder() {
        return new AnnotationConfigDOBuilder();
    }
    
    /**
     * Builder类
     */
    public static class AnnotationConfigDOBuilder {
        private Long columnId;
        private AnnotationLabelTypeEnum labelType;
        private List<String> labelOption;
        private Boolean isMultiSelected;
        private Boolean isRequired;
        private String displayName;
        
        public AnnotationConfigDOBuilder columnId(Long columnId) {
            this.columnId = columnId;
            return this;
        }
        public AnnotationConfigDOBuilder labelType(AnnotationLabelTypeEnum labelType) {
            this.labelType = labelType;
            return this;
        }
        public AnnotationConfigDOBuilder labelOption(List<String> labelOption) {
            this.labelOption = labelOption;
            return this;
        }
        public AnnotationConfigDOBuilder isMultiSelected(Boolean isMultiSelected) {
            this.isMultiSelected = isMultiSelected;
            return this;
        }
        public AnnotationConfigDOBuilder isRequired(Boolean isRequired) {
            this.isRequired = isRequired;
            return this;
        }
        public AnnotationConfigDOBuilder displayName(String displayName) {
            this.displayName = displayName;
            return this;
        }
        
        public AnnotationConfigDO build() {
            return new AnnotationConfigDO(columnId, labelType, labelOption, isMultiSelected, isRequired, displayName);
        }
    }
}