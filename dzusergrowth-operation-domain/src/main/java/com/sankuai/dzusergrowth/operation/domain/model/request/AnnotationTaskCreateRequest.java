package com.sankuai.dzusergrowth.operation.domain.model.request;

import com.sankuai.dzusergrowth.operation.api.enums.AnnotationTaskTypeEnum;
import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationConfigDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 标注任务创建请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationTaskCreateRequest {
    
    /**
     * 标注数据的数据集ID
     */
    private Long annotationDatasetId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 标注类型
     */
    private AnnotationTaskTypeEnum annotationType;
    
    /**
     * 标注配置
     */
    private List<AnnotationConfigDO> annotationConfig;
    
    /**
     * 展示选用的列
     */
    private List<Long> showColumn;
    
    /**
     * 任务标注人员
     */
    private String annotator;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 创建人ID
     */
    private Long creatorId;


    /**
     * 获取annotationDatasetId
     *
     * @return annotationDatasetId
     */
    public Long getAnnotationDatasetId() {
        return annotationDatasetId;
    }

    /**
     * 获取taskName
     *
     * @return taskName
     */
    public String getTaskName() {
        return taskName;
    }

    /**
     * 获取annotationType
     *
     * @return annotationType
     */
    public AnnotationTaskTypeEnum getAnnotationType() {
        return annotationType;
    }

    /**
     * 获取annotationConfig
     *
     * @return annotationConfig
     */
    public List<AnnotationConfigDO> getAnnotationConfig() {
        return annotationConfig;
    }

    /**
     * 获取showColumn
     *
     * @return showColumn
     */
    public List<Long> getShowColumn() {
        return showColumn;
    }

    /**
     * 获取annotator
     *
     * @return annotator
     */
    public String getAnnotator() {
        return annotator;
    }

    /**
     * 获取description
     *
     * @return description
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取creatorId
     *
     * @return creatorId
     */
    public Long getCreatorId() {
        return creatorId;
    }

    /**
     * 设置annotationDatasetId
     *
     * @param annotationDatasetId annotationDatasetId
     */
    public void setAnnotationDatasetId(Long annotationDatasetId) {
        this.annotationDatasetId = annotationDatasetId;
    }

    /**
     * 设置taskName
     *
     * @param taskName taskName
     */
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    /**
     * 设置annotationType
     *
     * @param annotationType annotationType
     */
    public void setAnnotationType(AnnotationTaskTypeEnum annotationType) {
        this.annotationType = annotationType;
    }

    /**
     * 设置annotationConfig
     *
     * @param annotationConfig annotationConfig
     */
    public void setAnnotationConfig(List<AnnotationConfigDO> annotationConfig) {
        this.annotationConfig = annotationConfig;
    }

    /**
     * 设置showColumn
     *
     * @param showColumn showColumn
     */
    public void setShowColumn(List<Long> showColumn) {
        this.showColumn = showColumn;
    }

    /**
     * 设置annotator
     *
     * @param annotator annotator
     */
    public void setAnnotator(String annotator) {
        this.annotator = annotator;
    }

    /**
     * 设置description
     *
     * @param description description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 设置creatorId
     *
     * @param creatorId creatorId
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static AnnotationTaskCreateRequestBuilder builder() {
        return new AnnotationTaskCreateRequestBuilder();
    }
    
    /**
     * Builder类
     */
    public static class AnnotationTaskCreateRequestBuilder {
        private Long annotationDatasetId;
        private String taskName;
        private AnnotationTaskTypeEnum annotationType;
        private List<AnnotationConfigDO> annotationConfig;
        private List<Long> showColumn;
        private String annotator;
        private String description;
        private Long creatorId;
        
        public AnnotationTaskCreateRequestBuilder annotationDatasetId(Long annotationDatasetId) {
            this.annotationDatasetId = annotationDatasetId;
            return this;
        }
        public AnnotationTaskCreateRequestBuilder taskName(String taskName) {
            this.taskName = taskName;
            return this;
        }
        public AnnotationTaskCreateRequestBuilder annotationType(AnnotationTaskTypeEnum annotationType) {
            this.annotationType = annotationType;
            return this;
        }
        public AnnotationTaskCreateRequestBuilder annotationConfig(List<AnnotationConfigDO> annotationConfig) {
            this.annotationConfig = annotationConfig;
            return this;
        }
        public AnnotationTaskCreateRequestBuilder showColumn(List<Long> showColumn) {
            this.showColumn = showColumn;
            return this;
        }
        public AnnotationTaskCreateRequestBuilder annotator(String annotator) {
            this.annotator = annotator;
            return this;
        }
        public AnnotationTaskCreateRequestBuilder description(String description) {
            this.description = description;
            return this;
        }
        public AnnotationTaskCreateRequestBuilder creatorId(Long creatorId) {
            this.creatorId = creatorId;
            return this;
        }
        
        public AnnotationTaskCreateRequest build() {
            return new AnnotationTaskCreateRequest(annotationDatasetId, taskName, annotationType, annotationConfig, showColumn, annotator, description, creatorId);
        }
    }
} 