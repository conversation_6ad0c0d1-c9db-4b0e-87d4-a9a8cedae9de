package com.sankuai.dzusergrowth.operation.domain.model.evaluation;

import com.sankuai.dzusergrowth.operation.api.enums.EvaluationTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.TaskStatusEnum;
import lombok.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 评测任务领域对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationTaskDO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 资源测的数据集ID
     */
    private Long sourceDatasetId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 评测类型: MANUAL-手动评测; AUTO-自动检验
     */
    private EvaluationTypeEnum evaluationType;
    
    /**
     * 评测指标计算配置列表
     */
    private List<EvaluationIndicatorConfig> evaluationConfig;
    
    /**
     * 产出数据集ID
     */
    private Long evaluationDatasetId;
    
    /**
     * 任务执行状态
     */
    private TaskStatusEnum status;
    
    /**
     * 统计结果
     */
    private Map<String, Object> evaluationData;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 添加时间
     */
    private Date addTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 验证任务名称不为空
     */
    public boolean isValidTaskName() {
        return taskName != null && !taskName.trim().isEmpty();
    }
    
    /**
     * 验证评测类型是否有效
     */
    public boolean isValidEvaluationType() {
        return evaluationType != null;
    }
    
    /**
     * 是否为手动评测
     */
    public boolean isManualEvaluation() {
        return evaluationType != null && evaluationType.isManual();
    }
    
    /**
     * 是否为自动检验
     */
    public boolean isAutoEvaluation() {
        return evaluationType != null && evaluationType.isAuto();
    }
    
    /**
     * 验证评测配置是否有效
     */
    public boolean isValidEvaluationConfig() {
        return evaluationConfig != null && !evaluationConfig.isEmpty() &&
               evaluationConfig.stream().allMatch(EvaluationIndicatorConfig::isValid);
    }
    
    /**
     * 验证状态是否有效
     */
    public boolean isValidStatus() {
        return status != null;
    }
    
    /**
     * 是否为执行中状态
     */
    public boolean isRunningStatus() {
        return status != null && status.isRunning();
    }
    
    /**
     * 是否为成功状态
     */
    public boolean isSuccessStatus() {
        return status != null && status.isSuccess();
    }
    
    /**
     * 是否为失败状态
     */
    public boolean isFailedStatus() {
        return status != null && status.isFailed();
    }
    
    /**
     * 任务是否已完成（成功或失败）
     */
    public boolean isFinished() {
        return status != null && status.isFinished();
    }


    /**
     * 获取id
     *
     * @return id
     */
    public Long getId() {
        return id;
    }

    /**
     * 获取sourceDatasetId
     *
     * @return sourceDatasetId
     */
    public Long getSourceDatasetId() {
        return sourceDatasetId;
    }

    /**
     * 获取taskName
     *
     * @return taskName
     */
    public String getTaskName() {
        return taskName;
    }

    /**
     * 获取evaluationType
     *
     * @return evaluationType
     */
    public EvaluationTypeEnum getEvaluationType() {
        return evaluationType;
    }

    /**
     * 获取evaluationConfig
     *
     * @return evaluationConfig
     */
    public List<EvaluationIndicatorConfig> getEvaluationConfig() {
        return evaluationConfig;
    }

    /**
     * 获取evaluationDatasetId
     *
     * @return evaluationDatasetId
     */
    public Long getEvaluationDatasetId() {
        return evaluationDatasetId;
    }

    /**
     * 获取status
     *
     * @return status
     */
    public TaskStatusEnum getStatus() {
        return status;
    }

    /**
     * 获取evaluationData
     *
     * @return evaluationData
     */
    public Map<String, Object> getEvaluationData() {
        return evaluationData;
    }

    /**
     * 获取creatorId
     *
     * @return creatorId
     */
    public Long getCreatorId() {
        return creatorId;
    }

    /**
     * 获取updaterId
     *
     * @return updaterId
     */
    public Long getUpdaterId() {
        return updaterId;
    }

    /**
     * 获取description
     *
     * @return description
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取addTime
     *
     * @return addTime
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * 获取updateTime
     *
     * @return updateTime
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置id
     *
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 设置sourceDatasetId
     *
     * @param sourceDatasetId sourceDatasetId
     */
    public void setSourceDatasetId(Long sourceDatasetId) {
        this.sourceDatasetId = sourceDatasetId;
    }

    /**
     * 设置taskName
     *
     * @param taskName taskName
     */
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    /**
     * 设置evaluationType
     *
     * @param evaluationType evaluationType
     */
    public void setEvaluationType(EvaluationTypeEnum evaluationType) {
        this.evaluationType = evaluationType;
    }

    /**
     * 设置evaluationConfig
     *
     * @param evaluationConfig evaluationConfig
     */
    public void setEvaluationConfig(List<EvaluationIndicatorConfig> evaluationConfig) {
        this.evaluationConfig = evaluationConfig;
    }

    /**
     * 设置evaluationDatasetId
     *
     * @param evaluationDatasetId evaluationDatasetId
     */
    public void setEvaluationDatasetId(Long evaluationDatasetId) {
        this.evaluationDatasetId = evaluationDatasetId;
    }

    /**
     * 设置status
     *
     * @param status status
     */
    public void setStatus(TaskStatusEnum status) {
        this.status = status;
    }

    /**
     * 设置evaluationData
     *
     * @param evaluationData evaluationData
     */
    public void setEvaluationData(Map<String, Object> evaluationData) {
        this.evaluationData = evaluationData;
    }

    /**
     * 设置creatorId
     *
     * @param creatorId creatorId
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 设置updaterId
     *
     * @param updaterId updaterId
     */
    public void setUpdaterId(Long updaterId) {
        this.updaterId = updaterId;
    }

    /**
     * 设置description
     *
     * @param description description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 设置addTime
     *
     * @param addTime addTime
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * 设置updateTime
     *
     * @param updateTime updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static EvaluationTaskDOBuilder builder() {
        return new EvaluationTaskDOBuilder();
    }
    
    /**
     * Builder类
     */
    public static class EvaluationTaskDOBuilder {
        private Long id;
        private Long sourceDatasetId;
        private String taskName;
        private EvaluationTypeEnum evaluationType;
        private List<EvaluationIndicatorConfig> evaluationConfig;
        private Long evaluationDatasetId;
        private TaskStatusEnum status;
        private Map<String, Object> evaluationData;
        private Long creatorId;
        private Long updaterId;
        private String description;
        private Date addTime;
        private Date updateTime;
        
        public EvaluationTaskDOBuilder id(Long id) {
            this.id = id;
            return this;
        }
        public EvaluationTaskDOBuilder sourceDatasetId(Long sourceDatasetId) {
            this.sourceDatasetId = sourceDatasetId;
            return this;
        }
        public EvaluationTaskDOBuilder taskName(String taskName) {
            this.taskName = taskName;
            return this;
        }
        public EvaluationTaskDOBuilder evaluationType(EvaluationTypeEnum evaluationType) {
            this.evaluationType = evaluationType;
            return this;
        }
        public EvaluationTaskDOBuilder evaluationConfig(List<EvaluationIndicatorConfig> evaluationConfig) {
            this.evaluationConfig = evaluationConfig;
            return this;
        }
        public EvaluationTaskDOBuilder evaluationDatasetId(Long evaluationDatasetId) {
            this.evaluationDatasetId = evaluationDatasetId;
            return this;
        }
        public EvaluationTaskDOBuilder status(TaskStatusEnum status) {
            this.status = status;
            return this;
        }
        public EvaluationTaskDOBuilder evaluationData(Map<String, Object> evaluationData) {
            this.evaluationData = evaluationData;
            return this;
        }
        public EvaluationTaskDOBuilder creatorId(Long creatorId) {
            this.creatorId = creatorId;
            return this;
        }
        public EvaluationTaskDOBuilder updaterId(Long updaterId) {
            this.updaterId = updaterId;
            return this;
        }
        public EvaluationTaskDOBuilder description(String description) {
            this.description = description;
            return this;
        }
        public EvaluationTaskDOBuilder addTime(Date addTime) {
            this.addTime = addTime;
            return this;
        }
        public EvaluationTaskDOBuilder updateTime(Date updateTime) {
            this.updateTime = updateTime;
            return this;
        }
        
        public EvaluationTaskDO build() {
            return new EvaluationTaskDO(id, sourceDatasetId, taskName, evaluationType, evaluationConfig, evaluationDatasetId, status, evaluationData, creatorId, updaterId, description, addTime, updateTime);
        }
    }
} 