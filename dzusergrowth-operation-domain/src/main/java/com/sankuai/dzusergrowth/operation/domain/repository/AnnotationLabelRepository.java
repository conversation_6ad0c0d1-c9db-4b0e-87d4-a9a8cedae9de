package com.sankuai.dzusergrowth.operation.domain.repository;

import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationLabelDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.AnnotationLabelQuery;

import java.util.List;

/**
 * 标注标签仓储接口
 */
public interface AnnotationLabelRepository {
    
    /**
     * 创建新的标注标签
     * 
     * @param annotationLabelDO 标注标签信息DO
     */
    void createAnnotationLabel(AnnotationLabelDO annotationLabelDO);
    
    /**
     * 根据ID获取标注标签
     * 
     * @param labelId 标签ID
     * @return 标注标签信息，如不存在则返回null
     */
    AnnotationLabelDO getAnnotationLabelById(Long labelId);
    
    /**
     * 更新标注标签
     * 
     * @param annotationLabelDO 标注标签DO
     */
    void updateAnnotationLabel(AnnotationLabelDO annotationLabelDO);
    
    /**
     * 删除标注标签
     * 
     * @param labelId 标签ID
     */
    void deleteAnnotationLabel(Long labelId);
    
    /**
     * 批量查询标注标签
     * 
     * @param query 标注标签查询条件
     * @return 标注标签DO列表
     */
    List<AnnotationLabelDO> queryAnnotationLabel(AnnotationLabelQuery query);
} 