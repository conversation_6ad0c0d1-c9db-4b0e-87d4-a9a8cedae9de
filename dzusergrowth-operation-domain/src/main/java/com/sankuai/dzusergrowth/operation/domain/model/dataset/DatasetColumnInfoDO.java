package com.sankuai.dzusergrowth.operation.domain.model.dataset;

import com.sankuai.dzusergrowth.operation.api.enums.DataTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.ColumnTypeEnum;
import lombok.*;

import java.util.Date;

/**
 * 数据集列信息领域对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetColumnInfoDO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 数据集ID
     */
    private Long datasetId;
    
    /**
     * 列名
     */
    private String name;
    
    /**
     * 列展示名
     */
    private String displayName;
    
    /**
     * 数据类型
     */
    private DataTypeEnum dataType;
    
    /**
     * 列类型
     */
    private ColumnTypeEnum columnType;
    
    /**
     * 列配置对象
     * 在存储时会转换为JSON字符串
     */
    private DatasetColumnConfig columnConfig;

    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 添加时间
     */
    private Date addTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 验证列名不为空
     */
    public boolean isValidName() {
        return name != null && !name.trim().isEmpty();
    }
    
    /**
     * 验证数据类型是否有效
     */
    public boolean isValidDataType() {
        return dataType != null;
    }
    
    /**
     * 验证列类型是否有效
     */
    public boolean isValidColumnType() {
        return columnType != null;
    }



    /**
     * 获取id
     *
     * @return id
     */
    public Long getId() {
        return id;
    }

    /**
     * 获取datasetId
     *
     * @return datasetId
     */
    public Long getDatasetId() {
        return datasetId;
    }

    /**
     * 获取name
     *
     * @return name
     */
    public String getName() {
        return name;
    }

    /**
     * 获取displayName
     *
     * @return displayName
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取dataType
     *
     * @return dataType
     */
    public DataTypeEnum getDataType() {
        return dataType;
    }

    /**
     * 获取columnType
     *
     * @return columnType
     */
    public ColumnTypeEnum getColumnType() {
        return columnType;
    }

    /**
     * 获取columnConfig
     *
     * @return columnConfig
     */
    public DatasetColumnConfig getColumnConfig() {
        return columnConfig;
    }

    /**
     * 获取creatorId
     *
     * @return creatorId
     */
    public Long getCreatorId() {
        return creatorId;
    }

    /**
     * 获取updaterId
     *
     * @return updaterId
     */
    public Long getUpdaterId() {
        return updaterId;
    }

    /**
     * 获取addTime
     *
     * @return addTime
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * 获取updateTime
     *
     * @return updateTime
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置id
     *
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 设置datasetId
     *
     * @param datasetId datasetId
     */
    public void setDatasetId(Long datasetId) {
        this.datasetId = datasetId;
    }

    /**
     * 设置name
     *
     * @param name name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 设置displayName
     *
     * @param displayName displayName
     */
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    /**
     * 设置dataType
     *
     * @param dataType dataType
     */
    public void setDataType(DataTypeEnum dataType) {
        this.dataType = dataType;
    }

    /**
     * 设置columnType
     *
     * @param columnType columnType
     */
    public void setColumnType(ColumnTypeEnum columnType) {
        this.columnType = columnType;
    }

    /**
     * 设置columnConfig
     *
     * @param columnConfig columnConfig
     */
    public void setColumnConfig(DatasetColumnConfig columnConfig) {
        this.columnConfig = columnConfig;
    }

    /**
     * 设置creatorId
     *
     * @param creatorId creatorId
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 设置updaterId
     *
     * @param updaterId updaterId
     */
    public void setUpdaterId(Long updaterId) {
        this.updaterId = updaterId;
    }

    /**
     * 设置addTime
     *
     * @param addTime addTime
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * 设置updateTime
     *
     * @param updateTime updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static DatasetColumnInfoDOBuilder builder() {
        return new DatasetColumnInfoDOBuilder();
    }
    
    /**
     * Builder类
     */
    public static class DatasetColumnInfoDOBuilder {
        private Long id;
        private Long datasetId;
        private String name;
        private String displayName;
        private DataTypeEnum dataType;
        private ColumnTypeEnum columnType;
        private DatasetColumnConfig columnConfig;
        private Long creatorId;
        private Long updaterId;
        private Date addTime;
        private Date updateTime;
        
        public DatasetColumnInfoDOBuilder id(Long id) {
            this.id = id;
            return this;
        }
        public DatasetColumnInfoDOBuilder datasetId(Long datasetId) {
            this.datasetId = datasetId;
            return this;
        }
        public DatasetColumnInfoDOBuilder name(String name) {
            this.name = name;
            return this;
        }
        public DatasetColumnInfoDOBuilder displayName(String displayName) {
            this.displayName = displayName;
            return this;
        }
        public DatasetColumnInfoDOBuilder dataType(DataTypeEnum dataType) {
            this.dataType = dataType;
            return this;
        }
        public DatasetColumnInfoDOBuilder columnType(ColumnTypeEnum columnType) {
            this.columnType = columnType;
            return this;
        }
        public DatasetColumnInfoDOBuilder columnConfig(DatasetColumnConfig columnConfig) {
            this.columnConfig = columnConfig;
            return this;
        }
        public DatasetColumnInfoDOBuilder creatorId(Long creatorId) {
            this.creatorId = creatorId;
            return this;
        }
        public DatasetColumnInfoDOBuilder updaterId(Long updaterId) {
            this.updaterId = updaterId;
            return this;
        }
        public DatasetColumnInfoDOBuilder addTime(Date addTime) {
            this.addTime = addTime;
            return this;
        }
        public DatasetColumnInfoDOBuilder updateTime(Date updateTime) {
            this.updateTime = updateTime;
            return this;
        }
        
        public DatasetColumnInfoDO build() {
            return new DatasetColumnInfoDO(id, datasetId, name, displayName, dataType, columnType, columnConfig, creatorId, updaterId, addTime, updateTime);
        }
    }
} 