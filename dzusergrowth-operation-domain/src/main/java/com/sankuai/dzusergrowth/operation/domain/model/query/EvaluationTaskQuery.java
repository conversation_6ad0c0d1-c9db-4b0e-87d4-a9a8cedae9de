package com.sankuai.dzusergrowth.operation.domain.model.query;

import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 评测任务查询条件
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationTaskQuery {
    
    /**
     * 任务ID列表
     */
    private List<Long> taskIds;
    
    /**
     * 资源测的数据集ID
     */
    private Long sourceDatasetId;
    
    /**
     * 任务名称（模糊查询）
     */
    private String taskName;
    
    /**
     * 评测类型: 1-手动评测; 2-自动检验
     */
    private Integer evaluationType;
    
    /**
     * 产出数据集ID
     */
    private Long evaluationDatasetId;
    
    /**
     * 任务执行状态
     */
    private Integer status;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建时间开始
     */
    private Date addTimeStart;
    
    /**
     * 创建时间结束
     */
    private Date addTimeEnd;
    
    /**
     * 分页页码（从1开始）
     */
    private Integer pageNum;
    
    /**
     * 分页大小
     */
    private Integer pageSize;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向: ASC/DESC
     */
    private String orderDirection;


    /**
     * 获取taskIds
     *
     * @return taskIds
     */
    public List<Long> getTaskIds() {
        return taskIds;
    }

    /**
     * 获取sourceDatasetId
     *
     * @return sourceDatasetId
     */
    public Long getSourceDatasetId() {
        return sourceDatasetId;
    }

    /**
     * 获取taskName
     *
     * @return taskName
     */
    public String getTaskName() {
        return taskName;
    }

    /**
     * 获取evaluationType
     *
     * @return evaluationType
     */
    public Integer getEvaluationType() {
        return evaluationType;
    }

    /**
     * 获取evaluationDatasetId
     *
     * @return evaluationDatasetId
     */
    public Long getEvaluationDatasetId() {
        return evaluationDatasetId;
    }

    /**
     * 获取status
     *
     * @return status
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 获取creatorId
     *
     * @return creatorId
     */
    public Long getCreatorId() {
        return creatorId;
    }

    /**
     * 获取addTimeStart
     *
     * @return addTimeStart
     */
    public Date getAddTimeStart() {
        return addTimeStart;
    }

    /**
     * 获取addTimeEnd
     *
     * @return addTimeEnd
     */
    public Date getAddTimeEnd() {
        return addTimeEnd;
    }

    /**
     * 获取pageNum
     *
     * @return pageNum
     */
    public Integer getPageNum() {
        return pageNum;
    }

    /**
     * 获取pageSize
     *
     * @return pageSize
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * 获取orderBy
     *
     * @return orderBy
     */
    public String getOrderBy() {
        return orderBy;
    }

    /**
     * 获取orderDirection
     *
     * @return orderDirection
     */
    public String getOrderDirection() {
        return orderDirection;
    }

    /**
     * 设置taskIds
     *
     * @param taskIds taskIds
     */
    public void setTaskIds(List<Long> taskIds) {
        this.taskIds = taskIds;
    }

    /**
     * 设置sourceDatasetId
     *
     * @param sourceDatasetId sourceDatasetId
     */
    public void setSourceDatasetId(Long sourceDatasetId) {
        this.sourceDatasetId = sourceDatasetId;
    }

    /**
     * 设置taskName
     *
     * @param taskName taskName
     */
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    /**
     * 设置evaluationType
     *
     * @param evaluationType evaluationType
     */
    public void setEvaluationType(Integer evaluationType) {
        this.evaluationType = evaluationType;
    }

    /**
     * 设置evaluationDatasetId
     *
     * @param evaluationDatasetId evaluationDatasetId
     */
    public void setEvaluationDatasetId(Long evaluationDatasetId) {
        this.evaluationDatasetId = evaluationDatasetId;
    }

    /**
     * 设置status
     *
     * @param status status
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 设置creatorId
     *
     * @param creatorId creatorId
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 设置addTimeStart
     *
     * @param addTimeStart addTimeStart
     */
    public void setAddTimeStart(Date addTimeStart) {
        this.addTimeStart = addTimeStart;
    }

    /**
     * 设置addTimeEnd
     *
     * @param addTimeEnd addTimeEnd
     */
    public void setAddTimeEnd(Date addTimeEnd) {
        this.addTimeEnd = addTimeEnd;
    }

    /**
     * 设置pageNum
     *
     * @param pageNum pageNum
     */
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    /**
     * 设置pageSize
     *
     * @param pageSize pageSize
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 设置orderBy
     *
     * @param orderBy orderBy
     */
    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    /**
     * 设置orderDirection
     *
     * @param orderDirection orderDirection
     */
    public void setOrderDirection(String orderDirection) {
        this.orderDirection = orderDirection;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static EvaluationTaskQueryBuilder builder() {
        return new EvaluationTaskQueryBuilder();
    }
    
    /**
     * Builder类
     */
    public static class EvaluationTaskQueryBuilder {
        private List<Long> taskIds;
        private Long sourceDatasetId;
        private String taskName;
        private Integer evaluationType;
        private Long evaluationDatasetId;
        private Integer status;
        private Long creatorId;
        private Date addTimeStart;
        private Date addTimeEnd;
        private Integer pageNum;
        private Integer pageSize;
        private String orderBy;
        private String orderDirection;
        
        public EvaluationTaskQueryBuilder taskIds(List<Long> taskIds) {
            this.taskIds = taskIds;
            return this;
        }
        public EvaluationTaskQueryBuilder sourceDatasetId(Long sourceDatasetId) {
            this.sourceDatasetId = sourceDatasetId;
            return this;
        }
        public EvaluationTaskQueryBuilder taskName(String taskName) {
            this.taskName = taskName;
            return this;
        }
        public EvaluationTaskQueryBuilder evaluationType(Integer evaluationType) {
            this.evaluationType = evaluationType;
            return this;
        }
        public EvaluationTaskQueryBuilder evaluationDatasetId(Long evaluationDatasetId) {
            this.evaluationDatasetId = evaluationDatasetId;
            return this;
        }
        public EvaluationTaskQueryBuilder status(Integer status) {
            this.status = status;
            return this;
        }
        public EvaluationTaskQueryBuilder creatorId(Long creatorId) {
            this.creatorId = creatorId;
            return this;
        }
        public EvaluationTaskQueryBuilder addTimeStart(Date addTimeStart) {
            this.addTimeStart = addTimeStart;
            return this;
        }
        public EvaluationTaskQueryBuilder addTimeEnd(Date addTimeEnd) {
            this.addTimeEnd = addTimeEnd;
            return this;
        }
        public EvaluationTaskQueryBuilder pageNum(Integer pageNum) {
            this.pageNum = pageNum;
            return this;
        }
        public EvaluationTaskQueryBuilder pageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }
        public EvaluationTaskQueryBuilder orderBy(String orderBy) {
            this.orderBy = orderBy;
            return this;
        }
        public EvaluationTaskQueryBuilder orderDirection(String orderDirection) {
            this.orderDirection = orderDirection;
            return this;
        }
        
        public EvaluationTaskQuery build() {
            return new EvaluationTaskQuery(taskIds, sourceDatasetId, taskName, evaluationType, evaluationDatasetId, status, creatorId, addTimeStart, addTimeEnd, pageNum, pageSize, orderBy, orderDirection);
        }
    }
} 