package com.sankuai.dzusergrowth.operation.domain.model.request;

import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetColumnInfoDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数据集列批量操作请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetColumnsBatchOperationRequest {
    
    /**
     * 数据集ID
     */
    private Long datasetId;
    
    /**
     * 数据集列信息列表
     */
    private List<DatasetColumnInfoDO> columns;
    
    /**
     * 操作人ID
     */
    private Long operatorId;
    
    /**
     * 验证请求参数有效性
     */
    public boolean isValid() {
        // 基础字段验证
        if (datasetId == null) {
            return false;
        }
        
        if (operatorId == null) {
            return false;
        }
        
        if (columns == null || columns.isEmpty()) {
            return false;
        }
        
        // 验证每个列信息的有效性
        for (DatasetColumnInfoDO column : columns) {
            if (column == null || !column.isValidName() || !column.isValidDataType() || !column.isValidColumnType()) {
                return false;
            }
        }
        
        // 检查是否有重复的列名
        long distinctColumnNameCount = columns.stream()
                .map(DatasetColumnInfoDO::getName)
                .distinct()
                .count();
                
        if (distinctColumnNameCount != columns.size()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证数据集ID是否有效
     */
    public boolean hasValidDatasetId() {
        return datasetId != null && datasetId > 0;
    }
    
    /**
     * 验证列信息列表是否为空
     */
    public boolean hasColumns() {
        return columns != null && !columns.isEmpty();
    }
    
    /**
     * 获取列的数量
     */
    public int getColumnCount() {
        return columns != null ? columns.size() : 0;
    }


    /**
     * 设置datasetId
     *
     * @param datasetId datasetId
     */
    public void setDatasetId(Long datasetId) {
        this.datasetId = datasetId;
    }

    /**
     * 设置columns
     *
     * @param columns columns
     */
    public void setColumns(List<DatasetColumnInfoDO> columns) {
        this.columns = columns;
    }

    /**
     * 设置operatorId
     *
     * @param operatorId operatorId
     */
    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static DatasetColumnsBatchOperationRequestBuilder builder() {
        return new DatasetColumnsBatchOperationRequestBuilder();
    }
    
    /**
     * Builder类
     */
    public static class DatasetColumnsBatchOperationRequestBuilder {
        private Long datasetId;
        private List<DatasetColumnInfoDO> columns;
        private Long operatorId;
        
        public DatasetColumnsBatchOperationRequestBuilder datasetId(Long datasetId) {
            this.datasetId = datasetId;
            return this;
        }
        public DatasetColumnsBatchOperationRequestBuilder columns(List<DatasetColumnInfoDO> columns) {
            this.columns = columns;
            return this;
        }
        public DatasetColumnsBatchOperationRequestBuilder operatorId(Long operatorId) {
            this.operatorId = operatorId;
            return this;
        }
        
        public DatasetColumnsBatchOperationRequest build() {
            return new DatasetColumnsBatchOperationRequest(datasetId, columns, operatorId);
        }
    }
} 