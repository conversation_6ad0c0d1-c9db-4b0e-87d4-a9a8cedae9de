package com.sankuai.dzusergrowth.operation.domain.model.evaluation;

import lombok.*;

/**
 * 评测指标计算配置
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationIndicatorConfig {
    
    /**
     * 列ID（从数据集列信息表中映射得到）
     */
    private Long columnId;
    
    /**
     * 计算方式
     */
    private String calculationMethod;
    
    /**
     * 列展示名
     */
    private String displayName;
    
    /**
     * 验证配置是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return columnId != null && 
               calculationMethod != null && !calculationMethod.trim().isEmpty() &&
               displayName != null && !displayName.trim().isEmpty();
    }


    /**
     * 获取columnId
     *
     * @return columnId
     */
    public Long getColumnId() {
        return columnId;
    }

    /**
     * 获取calculationMethod
     *
     * @return calculationMethod
     */
    public String getCalculationMethod() {
        return calculationMethod;
    }

    /**
     * 获取displayName
     *
     * @return displayName
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 设置columnId
     *
     * @param columnId columnId
     */
    public void setColumnId(Long columnId) {
        this.columnId = columnId;
    }

    /**
     * 设置calculationMethod
     *
     * @param calculationMethod calculationMethod
     */
    public void setCalculationMethod(String calculationMethod) {
        this.calculationMethod = calculationMethod;
    }

    /**
     * 设置displayName
     *
     * @param displayName displayName
     */
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static EvaluationIndicatorConfigBuilder builder() {
        return new EvaluationIndicatorConfigBuilder();
    }
    
    /**
     * Builder类
     */
    public static class EvaluationIndicatorConfigBuilder {
        private Long columnId;
        private String calculationMethod;
        private String displayName;
        
        public EvaluationIndicatorConfigBuilder columnId(Long columnId) {
            this.columnId = columnId;
            return this;
        }
        public EvaluationIndicatorConfigBuilder calculationMethod(String calculationMethod) {
            this.calculationMethod = calculationMethod;
            return this;
        }
        public EvaluationIndicatorConfigBuilder displayName(String displayName) {
            this.displayName = displayName;
            return this;
        }
        
        public EvaluationIndicatorConfig build() {
            return new EvaluationIndicatorConfig(columnId, calculationMethod, displayName);
        }
    }
} 