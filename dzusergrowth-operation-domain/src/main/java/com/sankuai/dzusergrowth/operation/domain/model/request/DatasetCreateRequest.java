package com.sankuai.dzusergrowth.operation.domain.model.request;

import com.sankuai.dzusergrowth.operation.api.enums.DatasetTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.DataGranularityEnum;
import com.sankuai.dzusergrowth.operation.api.enums.GenerateTypeEnum;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetGenerateConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据集创建请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetCreateRequest {
    
    /**
     * 数据集类型
     */
    private DatasetTypeEnum datasetType;
    
    /**
     * 数据粒度
     */
    private DataGranularityEnum dataGranularity;
    
    /**
     * 数据集名称
     */
    private String name;
    
    /**
     * 数据集生成方式
     */
    private GenerateTypeEnum generateType;
    
    /**
     * 生成配置对象
     */
    private DatasetGenerateConfig generateConfig;
    
    /**
     * 数据集描述
     */
    private String description;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 验证创建请求参数有效性
     */
    public boolean isValid() {
        // 基础字段验证
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        
        if (datasetType == null) {
            return false;
        }
        
        if (dataGranularity == null) {
            return false;
        }
        
        if (generateType == null) {
            return false;
        }
        
        if (creatorId == null) {
            return false;
        }
        
        // 生成配置验证
        if (generateConfig == null || !generateConfig.isValid()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证数据集名称格式
     */
    public boolean hasValidName() {
        return name != null && !name.trim().isEmpty() && name.length() <= 100;
    }


    /**
     * 获取datasetType
     *
     * @return datasetType
     */
    public DatasetTypeEnum getDatasetType() {
        return datasetType;
    }

    /**
     * 获取dataGranularity
     *
     * @return dataGranularity
     */
    public DataGranularityEnum getDataGranularity() {
        return dataGranularity;
    }

    /**
     * 获取name
     *
     * @return name
     */
    public String getName() {
        return name;
    }

    /**
     * 获取generateType
     *
     * @return generateType
     */
    public GenerateTypeEnum getGenerateType() {
        return generateType;
    }

    /**
     * 获取generateConfig
     *
     * @return generateConfig
     */
    public DatasetGenerateConfig getGenerateConfig() {
        return generateConfig;
    }

    /**
     * 获取description
     *
     * @return description
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取creatorId
     *
     * @return creatorId
     */
    public Long getCreatorId() {
        return creatorId;
    }

    /**
     * 设置datasetType
     *
     * @param datasetType datasetType
     */
    public void setDatasetType(DatasetTypeEnum datasetType) {
        this.datasetType = datasetType;
    }

    /**
     * 设置dataGranularity
     *
     * @param dataGranularity dataGranularity
     */
    public void setDataGranularity(DataGranularityEnum dataGranularity) {
        this.dataGranularity = dataGranularity;
    }

    /**
     * 设置name
     *
     * @param name name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 设置generateType
     *
     * @param generateType generateType
     */
    public void setGenerateType(GenerateTypeEnum generateType) {
        this.generateType = generateType;
    }

    /**
     * 设置generateConfig
     *
     * @param generateConfig generateConfig
     */
    public void setGenerateConfig(DatasetGenerateConfig generateConfig) {
        this.generateConfig = generateConfig;
    }

    /**
     * 设置description
     *
     * @param description description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 设置creatorId
     *
     * @param creatorId creatorId
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static DatasetCreateRequestBuilder builder() {
        return new DatasetCreateRequestBuilder();
    }
    
    /**
     * Builder类
     */
    public static class DatasetCreateRequestBuilder {
        private DatasetTypeEnum datasetType;
        private DataGranularityEnum dataGranularity;
        private String name;
        private GenerateTypeEnum generateType;
        private DatasetGenerateConfig generateConfig;
        private String description;
        private Long creatorId;
        
        public DatasetCreateRequestBuilder datasetType(DatasetTypeEnum datasetType) {
            this.datasetType = datasetType;
            return this;
        }
        public DatasetCreateRequestBuilder dataGranularity(DataGranularityEnum dataGranularity) {
            this.dataGranularity = dataGranularity;
            return this;
        }
        public DatasetCreateRequestBuilder name(String name) {
            this.name = name;
            return this;
        }
        public DatasetCreateRequestBuilder generateType(GenerateTypeEnum generateType) {
            this.generateType = generateType;
            return this;
        }
        public DatasetCreateRequestBuilder generateConfig(DatasetGenerateConfig generateConfig) {
            this.generateConfig = generateConfig;
            return this;
        }
        public DatasetCreateRequestBuilder description(String description) {
            this.description = description;
            return this;
        }
        public DatasetCreateRequestBuilder creatorId(Long creatorId) {
            this.creatorId = creatorId;
            return this;
        }
        
        public DatasetCreateRequest build() {
            return new DatasetCreateRequest(datasetType, dataGranularity, name, generateType, generateConfig, description, creatorId);
        }
    }
} 