package com.sankuai.dzusergrowth.operation.domain.model.query;

import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 标注标签查询条件
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationLabelQuery {
    
    /**
     * 标签ID列表
     */
    private List<Long> labelIds;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建时间开始
     */
    private Date addTimeStart;
    
    /**
     * 创建时间结束
     */
    private Date addTimeEnd;
    
    /**
     * 分页页码（从1开始）
     */
    private Integer pageNum;
    
    /**
     * 分页大小
     */
    private Integer pageSize;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向: ASC/DESC
     */
    private String orderDirection;


    /**
     * 获取labelIds
     *
     * @return labelIds
     */
    public List<Long> getLabelIds() {
        return labelIds;
    }

    /**
     * 获取creatorId
     *
     * @return creatorId
     */
    public Long getCreatorId() {
        return creatorId;
    }

    /**
     * 获取addTimeStart
     *
     * @return addTimeStart
     */
    public Date getAddTimeStart() {
        return addTimeStart;
    }

    /**
     * 获取addTimeEnd
     *
     * @return addTimeEnd
     */
    public Date getAddTimeEnd() {
        return addTimeEnd;
    }

    /**
     * 获取pageNum
     *
     * @return pageNum
     */
    public Integer getPageNum() {
        return pageNum;
    }

    /**
     * 获取pageSize
     *
     * @return pageSize
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * 获取orderBy
     *
     * @return orderBy
     */
    public String getOrderBy() {
        return orderBy;
    }

    /**
     * 获取orderDirection
     *
     * @return orderDirection
     */
    public String getOrderDirection() {
        return orderDirection;
    }

    /**
     * 设置labelIds
     *
     * @param labelIds labelIds
     */
    public void setLabelIds(List<Long> labelIds) {
        this.labelIds = labelIds;
    }

    /**
     * 设置creatorId
     *
     * @param creatorId creatorId
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 设置addTimeStart
     *
     * @param addTimeStart addTimeStart
     */
    public void setAddTimeStart(Date addTimeStart) {
        this.addTimeStart = addTimeStart;
    }

    /**
     * 设置addTimeEnd
     *
     * @param addTimeEnd addTimeEnd
     */
    public void setAddTimeEnd(Date addTimeEnd) {
        this.addTimeEnd = addTimeEnd;
    }

    /**
     * 设置pageNum
     *
     * @param pageNum pageNum
     */
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    /**
     * 设置pageSize
     *
     * @param pageSize pageSize
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 设置orderBy
     *
     * @param orderBy orderBy
     */
    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    /**
     * 设置orderDirection
     *
     * @param orderDirection orderDirection
     */
    public void setOrderDirection(String orderDirection) {
        this.orderDirection = orderDirection;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static AnnotationLabelQueryBuilder builder() {
        return new AnnotationLabelQueryBuilder();
    }
    
    /**
     * Builder类
     */
    public static class AnnotationLabelQueryBuilder {
        private List<Long> labelIds;
        private Long creatorId;
        private Date addTimeStart;
        private Date addTimeEnd;
        private Integer pageNum;
        private Integer pageSize;
        private String orderBy;
        private String orderDirection;
        
        public AnnotationLabelQueryBuilder labelIds(List<Long> labelIds) {
            this.labelIds = labelIds;
            return this;
        }
        public AnnotationLabelQueryBuilder creatorId(Long creatorId) {
            this.creatorId = creatorId;
            return this;
        }
        public AnnotationLabelQueryBuilder addTimeStart(Date addTimeStart) {
            this.addTimeStart = addTimeStart;
            return this;
        }
        public AnnotationLabelQueryBuilder addTimeEnd(Date addTimeEnd) {
            this.addTimeEnd = addTimeEnd;
            return this;
        }
        public AnnotationLabelQueryBuilder pageNum(Integer pageNum) {
            this.pageNum = pageNum;
            return this;
        }
        public AnnotationLabelQueryBuilder pageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }
        public AnnotationLabelQueryBuilder orderBy(String orderBy) {
            this.orderBy = orderBy;
            return this;
        }
        public AnnotationLabelQueryBuilder orderDirection(String orderDirection) {
            this.orderDirection = orderDirection;
            return this;
        }
        
        public AnnotationLabelQuery build() {
            return new AnnotationLabelQuery(labelIds, creatorId, addTimeStart, addTimeEnd, pageNum, pageSize, orderBy, orderDirection);
        }
    }
} 