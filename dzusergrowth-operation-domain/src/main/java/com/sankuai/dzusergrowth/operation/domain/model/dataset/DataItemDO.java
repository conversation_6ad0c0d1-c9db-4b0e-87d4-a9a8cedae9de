package com.sankuai.dzusergrowth.operation.domain.model.dataset;

import lombok.*;

import java.util.Date;
import java.util.Map;

/**
 * 数据条目领域对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataItemDO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 数据集ID
     */
    private Long datasetId;
    
    /**
     * 数据唯一键-标识: 比如TraceID，活动ID等
     */
    private String dataUniqueKey;

    
    /**
     * 数据内容映射
     * key: 列ID，value: 列的数据值
     * 在存储时会转换为JSON字符串
     */
    private Map<Long, String> data;

    
    /**
     * 软删除标记
     */
    private Boolean isDeleted;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 添加时间
     */
    private Date addTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 验证数据唯一键不为空
     */
    public boolean isValidDataUniqueKey() {
        return dataUniqueKey != null && !dataUniqueKey.trim().isEmpty();
    }
    
    /**
     * 验证数据内容不为空
     */
    public boolean isValidData() {
        return data != null && !data.isEmpty();
    }
    
    /**
     * 检查是否已删除
     */
    public boolean isDeleted() {
        return Boolean.TRUE.equals(isDeleted);
    }
    
    /**
     * 标记为删除
     */
    public void markAsDeleted() {
        this.isDeleted = true;
    }
    
    /**
     * 根据列ID获取数据值
     * 
     * @param columnId 列ID
     * @return 对应的数据值，如果不存在则返回null
     */
    public String getDataByColumnId(Long columnId) {
        if (data == null || columnId == null) {
            return null;
        }
        return data.get(columnId);
    }

    /**
     * 设置指定列的数据值
     * 
     * @param columnId 列ID
     * @param value 数据值
     */
    public void setDataByColumnId(Long columnId, String value) {
        if (data == null) {
            data = new java.util.HashMap<>();
        }
        data.put(columnId, value);
    }
    
    /**
     * 移除指定列的数据
     * 
     * @param columnId 列ID
     * @return 被移除的数据值
     */
    public String removeDataByColumnId(Long columnId) {
        if (data == null || columnId == null) {
            return null;
        }
        return data.remove(columnId);
    }


    /**
     * 获取id
     *
     * @return id
     */
    public Long getId() {
        return id;
    }

    /**
     * 获取datasetId
     *
     * @return datasetId
     */
    public Long getDatasetId() {
        return datasetId;
    }

    /**
     * 获取dataUniqueKey
     *
     * @return dataUniqueKey
     */
    public String getDataUniqueKey() {
        return dataUniqueKey;
    }

    /**
     * 获取data
     *
     * @return data
     */
    public Map<Long, String> getData() {
        return data;
    }

    /**
     * 获取isDeleted
     *
     * @return isDeleted
     */
    public Boolean isDeleted() {
        return isDeleted;
    }

    /**
     * 获取creatorId
     *
     * @return creatorId
     */
    public Long getCreatorId() {
        return creatorId;
    }

    /**
     * 获取updaterId
     *
     * @return updaterId
     */
    public Long getUpdaterId() {
        return updaterId;
    }

    /**
     * 获取addTime
     *
     * @return addTime
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * 获取updateTime
     *
     * @return updateTime
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static DataItemDOBuilder builder() {
        return new DataItemDOBuilder();
    }
    
    /**
     * Builder类
     */
    public static class DataItemDOBuilder {
        private Long id;
        private Long datasetId;
        private String dataUniqueKey;
        private Map<Long, String> data;
        private Boolean isDeleted;
        private Long creatorId;
        private Long updaterId;
        private Date addTime;
        private Date updateTime;
        
        public DataItemDOBuilder id(Long id) {
            this.id = id;
            return this;
        }
        public DataItemDOBuilder datasetId(Long datasetId) {
            this.datasetId = datasetId;
            return this;
        }
        public DataItemDOBuilder dataUniqueKey(String dataUniqueKey) {
            this.dataUniqueKey = dataUniqueKey;
            return this;
        }
        public DataItemDOBuilder data(Map<Long, String> data) {
            this.data = data;
            return this;
        }
        public DataItemDOBuilder isDeleted(Boolean isDeleted) {
            this.isDeleted = isDeleted;
            return this;
        }
        public DataItemDOBuilder creatorId(Long creatorId) {
            this.creatorId = creatorId;
            return this;
        }
        public DataItemDOBuilder updaterId(Long updaterId) {
            this.updaterId = updaterId;
            return this;
        }
        public DataItemDOBuilder addTime(Date addTime) {
            this.addTime = addTime;
            return this;
        }
        public DataItemDOBuilder updateTime(Date updateTime) {
            this.updateTime = updateTime;
            return this;
        }
        
        public DataItemDO build() {
            return new DataItemDO(id, datasetId, dataUniqueKey, data, isDeleted, creatorId, updaterId, addTime, updateTime);
        }
    }
} 