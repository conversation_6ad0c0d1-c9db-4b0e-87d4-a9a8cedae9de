package com.sankuai.dzusergrowth.operation.domain.model.request;

import lombok.*;

/**
 * 数据条目查询请求
 * 用于根据数据集ID分页查询数据条目
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataItemQueryRequest {
    
    /**
     * 数据集ID（必填）
     */
    private Long datasetId;
    
    /**
     * 分页页码（从1开始）
     * 如果为空，默认为第1页
     */
    private Integer pageNum;
    
    /**
     * 分页大小
     * 如果为空，默认为10条
     */
    private Integer pageSize;
    
    /**
     * 排序字段（可选）
     * 如果为空，默认按创建时间排序
     */
    private String orderBy;
    
    /**
     * 排序方向: ASC/DESC（可选）
     * 如果为空，默认为DESC
     */
    private String orderDirection;
    
    /**
     * 验证请求参数是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        // 数据集ID必须存在
        if (datasetId == null || datasetId <= 0) {
            return false;
        }
        
        // 页码必须大于0
        if (pageNum != null && pageNum < 1) {
            return false;
        }
        
        // 页面大小必须大于0且不超过100
        if (pageSize != null && (pageSize < 1 || pageSize > 100)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取有效的分页页码
     *
     * @return 有效的页码，默认为1
     */
    public int getValidPageNum() {
        return pageNum != null && pageNum > 0 ? pageNum : 1;
    }
    
    /**
     * 获取有效的分页大小
     *
     * @return 有效的页面大小，默认为10
     */
    public int getValidPageSize() {
        if (pageSize == null || pageSize < 1) {
            return 10;
        }
        return Math.min(pageSize, 100); // 最大不超过100
    }
    
    /**
     * 获取有效的排序字段
     *
     * @return 排序字段，默认为add_time
     */
    public String getValidOrderBy() {
        return orderBy != null && !orderBy.trim().isEmpty() ? orderBy.trim() : "add_time";
    }
    
    /**
     * 获取有效的排序方向
     *
     * @return 排序方向，默认为DESC
     */
    public String getValidOrderDirection() {
        if (orderDirection == null || orderDirection.trim().isEmpty()) {
            return "DESC";
        }
        String direction = orderDirection.trim().toUpperCase();
        return "ASC".equals(direction) || "DESC".equals(direction) ? direction : "DESC";
    }
    
    /**
     * 创建基础查询请求（只包含必要参数）
     *
     * @param datasetId 数据集ID
     * @return 基础查询请求
     */
    public static DataItemQueryRequest basic(Long datasetId) {
        return DataItemQueryRequest.builder()
                .datasetId(datasetId)
                .pageNum(1)
                .pageSize(10)
                .build();
    }
    
    /**
     * 创建带分页的查询请求
     *
     * @param datasetId 数据集ID
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 查询请求
     */
    public static DataItemQueryRequest create(Long datasetId, Integer pageNum, Integer pageSize) {
        return DataItemQueryRequest.builder()
                .datasetId(datasetId)
                .pageNum(pageNum)
                .pageSize(pageSize)
                .build();
    }
} 