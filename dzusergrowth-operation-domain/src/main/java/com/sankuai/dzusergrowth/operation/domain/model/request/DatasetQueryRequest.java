package com.sankuai.dzusergrowth.operation.domain.model.request;

import com.sankuai.dzusergrowth.operation.api.enums.DatasetTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.DataGranularityEnum;
import com.sankuai.dzusergrowth.operation.api.enums.GenerateTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.AssetTypeEnum;
import com.sankuai.dzusergrowth.ai.assistant.api.enums.ActivityTypeEnum;
import lombok.*;

import java.util.Date;

/**
 * 数据集查询请求
 * 支持根据数据集名称、数据粒度、活动类型、资产类型、创建者ID进行查询
 * 如果不设置具体条件，则查询全部数据集
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetQueryRequest {
    
    /**
     * 数据集名称（模糊查询）
     * 如果为空，则不按名称过滤
     */
    private String name;
    
    /**
     * 数据集类型
     * 如果为空，则不按数据集类型过滤
     */
    private DatasetTypeEnum datasetType;
    
    /**
     * 数据粒度
     * 如果为空，则不按数据粒度过滤
     */
    private DataGranularityEnum dataGranularity;

    /**
     * 活动类型
     * 如果为空，则不按活动类型过滤
     */
    private ActivityTypeEnum activityType;

    /**
     * 资产类型
     * 如果为空，则不按资产类型过滤
     */
    private AssetTypeEnum assetType;
    
    /**
     * 生成方式
     * 如果为空，则不按生成方式过滤
     */
    private GenerateTypeEnum generateType;
    
    /**
     * 创建人ID
     * 如果为空，则不按创建者过滤
     */
    private Long creatorId;
    
    /**
     * 创建时间开始（可选）
     */
    private Date addTimeStart;
    
    /**
     * 创建时间结束（可选）
     */
    private Date addTimeEnd;
    
    /**
     * 分页页码（从1开始）
     * 如果为空，默认为第1页
     */
    private Integer pageNum;
    
    /**
     * 分页大小
     * 如果为空，默认为10条
     */
    private Integer pageSize;
    
    /**
     * 排序字段
     * 如果为空，默认按创建时间排序
     */
    private String orderBy;
    
    /**
     * 排序方向: ASC/DESC
     * 如果为空，默认为DESC
     */
    private String orderDirection;
    
    /**
     * 通过数据粒度枚举设置查询条件
     *
     * @param dataGranularityEnum 数据粒度枚举
     * @return 当前查询对象，支持链式调用
     */
    public DatasetQueryRequest setDataGranularity(DataGranularityEnum dataGranularityEnum) {
        this.dataGranularity = dataGranularityEnum;
        return this;
    }
    
    /**
     * 通过资产类型枚举设置查询条件
     *
     * @param assetTypeEnum 资产类型枚举
     * @return 当前查询对象，支持链式调用
     */
    public DatasetQueryRequest setAssetType(AssetTypeEnum assetTypeEnum) {
        this.assetType = assetTypeEnum;
        return this;
    }
    
    /**
     * 验证请求参数是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        // 页码必须大于0
        if (pageNum != null && pageNum < 1) {
            return false;
        }
        
        // 页面大小必须大于0且不超过100
        if (pageSize != null && (pageSize < 1 || pageSize > 100)) {
            return false;
        }
        
        // 时间范围校验
        if (addTimeStart != null && addTimeEnd != null && addTimeStart.after(addTimeEnd)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 判断是否为空查询（查询全部数据集）
     *
     * @return 如果所有查询条件都为空，返回true
     */
    public boolean isEmptyQuery() {
        return name == null 
               && datasetType == null
               && dataGranularity == null 
               && activityType == null 
               && assetType == null 
               && generateType == null
               && creatorId == null 
               && addTimeStart == null 
               && addTimeEnd == null;
    }
    
    /**
     * 获取有效的分页页码
     *
     * @return 有效的页码，默认为1
     */
    public int getValidPageNum() {
        return pageNum != null && pageNum > 0 ? pageNum : 1;
    }
    
    /**
     * 获取有效的分页大小
     *
     * @return 有效的页面大小，默认为10
     */
    public int getValidPageSize() {
        if (pageSize == null || pageSize < 1) {
            return 10;
        }
        return Math.min(pageSize, 100); // 最大不超过100
    }
} 