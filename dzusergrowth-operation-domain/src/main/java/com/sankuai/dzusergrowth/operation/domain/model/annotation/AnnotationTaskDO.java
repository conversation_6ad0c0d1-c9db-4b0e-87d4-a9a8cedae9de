package com.sankuai.dzusergrowth.operation.domain.model.annotation;

import com.sankuai.dzusergrowth.operation.api.enums.AnnotationTaskTypeEnum;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 标注任务领域对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationTaskDO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 标注数据的数据集ID
     */
    private Long annotationDatasetId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 标注类型
     */
    private AnnotationTaskTypeEnum annotationTaskType;
    
    /**
     * 标注配置
     */
    private List<AnnotationConfigDO> annotationConfig;
    
    /**
     * 展示选用的列
     */
    private List<Long> showColumn;
    
    /**
     * 任务标注人员
     */
    private String annotator;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 添加时间
     */
    private Date addTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 验证任务名称不为空
     */
    public boolean isValidTaskName() {
        return taskName != null && !taskName.trim().isEmpty();
    }
    
    /**
     * 验证标注类型是否有效
     */
    public boolean isValidAnnotationType() {
        return annotationTaskType != null;
    }
    
    /**
     * 是否为人工标注
     */
    public boolean isManualAnnotation() {
        return AnnotationTaskTypeEnum.MANUAL.equals(annotationTaskType);
    }
    
    /**
     * 是否为自动标注
     */
    public boolean isAutoAnnotation() {
        return AnnotationTaskTypeEnum.AUTO.equals(annotationTaskType);
    }

    
    /**
     * 获取配置中的所有标注列ID
     */
    public List<Long> getLabelIds() {
        if (annotationConfig == null) {
            return List.of();
        }
        
        return annotationConfig.stream()
                .map(AnnotationConfigDO::getColumnId)
                .filter(java.util.Objects::nonNull)
                .toList();
    }


    /**
     * 获取id
     *
     * @return id
     */
    public Long getId() {
        return id;
    }

    /**
     * 获取annotationDatasetId
     *
     * @return annotationDatasetId
     */
    public Long getAnnotationDatasetId() {
        return annotationDatasetId;
    }

    /**
     * 获取taskName
     *
     * @return taskName
     */
    public String getTaskName() {
        return taskName;
    }

    /**
     * 获取annotationTaskType
     *
     * @return annotationTaskType
     */
    public AnnotationTaskTypeEnum getAnnotationTaskType() {
        return annotationTaskType;
    }

    /**
     * 获取annotationConfig
     *
     * @return annotationConfig
     */
    public List<AnnotationConfigDO> getAnnotationConfig() {
        return annotationConfig;
    }

    /**
     * 获取showColumn
     *
     * @return showColumn
     */
    public List<Long> getShowColumn() {
        return showColumn;
    }

    /**
     * 获取annotator
     *
     * @return annotator
     */
    public String getAnnotator() {
        return annotator;
    }

    /**
     * 获取creatorId
     *
     * @return creatorId
     */
    public Long getCreatorId() {
        return creatorId;
    }

    /**
     * 获取updaterId
     *
     * @return updaterId
     */
    public Long getUpdaterId() {
        return updaterId;
    }

    /**
     * 获取description
     *
     * @return description
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取addTime
     *
     * @return addTime
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * 获取updateTime
     *
     * @return updateTime
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置id
     *
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 设置annotationDatasetId
     *
     * @param annotationDatasetId annotationDatasetId
     */
    public void setAnnotationDatasetId(Long annotationDatasetId) {
        this.annotationDatasetId = annotationDatasetId;
    }

    /**
     * 设置taskName
     *
     * @param taskName taskName
     */
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    /**
     * 设置annotationTaskType
     *
     * @param annotationTaskType annotationTaskType
     */
    public void setAnnotationTaskType(AnnotationTaskTypeEnum annotationTaskType) {
        this.annotationTaskType = annotationTaskType;
    }

    /**
     * 设置annotationConfig
     *
     * @param annotationConfig annotationConfig
     */
    public void setAnnotationConfig(List<AnnotationConfigDO> annotationConfig) {
        this.annotationConfig = annotationConfig;
    }

    /**
     * 设置showColumn
     *
     * @param showColumn showColumn
     */
    public void setShowColumn(List<Long> showColumn) {
        this.showColumn = showColumn;
    }

    /**
     * 设置annotator
     *
     * @param annotator annotator
     */
    public void setAnnotator(String annotator) {
        this.annotator = annotator;
    }

    /**
     * 设置creatorId
     *
     * @param creatorId creatorId
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 设置updaterId
     *
     * @param updaterId updaterId
     */
    public void setUpdaterId(Long updaterId) {
        this.updaterId = updaterId;
    }

    /**
     * 设置description
     *
     * @param description description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 设置addTime
     *
     * @param addTime addTime
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * 设置updateTime
     *
     * @param updateTime updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static AnnotationTaskDOBuilder builder() {
        return new AnnotationTaskDOBuilder();
    }
    
    /**
     * Builder类
     */
    public static class AnnotationTaskDOBuilder {
        private Long id;
        private Long annotationDatasetId;
        private String taskName;
        private AnnotationTaskTypeEnum annotationTaskType;
        private List<AnnotationConfigDO> annotationConfig;
        private List<Long> showColumn;
        private String annotator;
        private Long creatorId;
        private Long updaterId;
        private String description;
        private Date addTime;
        private Date updateTime;
        
        public AnnotationTaskDOBuilder id(Long id) {
            this.id = id;
            return this;
        }
        public AnnotationTaskDOBuilder annotationDatasetId(Long annotationDatasetId) {
            this.annotationDatasetId = annotationDatasetId;
            return this;
        }
        public AnnotationTaskDOBuilder taskName(String taskName) {
            this.taskName = taskName;
            return this;
        }
        public AnnotationTaskDOBuilder annotationTaskType(AnnotationTaskTypeEnum annotationTaskType) {
            this.annotationTaskType = annotationTaskType;
            return this;
        }
        public AnnotationTaskDOBuilder annotationConfig(List<AnnotationConfigDO> annotationConfig) {
            this.annotationConfig = annotationConfig;
            return this;
        }
        public AnnotationTaskDOBuilder showColumn(List<Long> showColumn) {
            this.showColumn = showColumn;
            return this;
        }
        public AnnotationTaskDOBuilder annotator(String annotator) {
            this.annotator = annotator;
            return this;
        }
        public AnnotationTaskDOBuilder creatorId(Long creatorId) {
            this.creatorId = creatorId;
            return this;
        }
        public AnnotationTaskDOBuilder updaterId(Long updaterId) {
            this.updaterId = updaterId;
            return this;
        }
        public AnnotationTaskDOBuilder description(String description) {
            this.description = description;
            return this;
        }
        public AnnotationTaskDOBuilder addTime(Date addTime) {
            this.addTime = addTime;
            return this;
        }
        public AnnotationTaskDOBuilder updateTime(Date updateTime) {
            this.updateTime = updateTime;
            return this;
        }
        
        public AnnotationTaskDO build() {
            return new AnnotationTaskDO(id, annotationDatasetId, taskName, annotationTaskType, annotationConfig, showColumn, annotator, creatorId, updaterId, description, addTime, updateTime);
        }
    }
} 