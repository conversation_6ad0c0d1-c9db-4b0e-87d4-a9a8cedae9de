package com.sankuai.dzusergrowth.operation.domain.repository;

import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.DatasetQuery;

import java.util.List;

/**
 * 数据集仓储接口
 */
public interface DatasetRepository {
    
    /**
     * 创建新的数据集
     * 
     * @param datasetDO 数据集信息DO
     */
    void createDataset(DatasetDO datasetDO);
    
    /**
     * 根据ID获取数据集
     * 
     * @param datasetId 数据集ID
     * @return 数据集信息，如不存在则返回null
     */
    DatasetDO getDatasetById(Long datasetId);
    
    /**
     * 更新数据集
     * 
     * @param datasetDO 数据集DO
     */
    void updateDataset(DatasetDO datasetDO);
    
    /**
     * 删除数据集
     * 
     * @param datasetId 数据集ID
     */
    void deleteDataset(Long datasetId);
    
    /**
     * 批量查询数据集
     * 
     * @param query 数据集查询条件
     * @return 数据集DO列表
     */
    List<DatasetDO> queryDataset(DatasetQuery query);
    
    /**
     * 统计符合条件的数据集数量（用于分页查询）
     * 
     * @param query 数据集查询条件
     * @return 数据集数量
     */
    long countDataset(DatasetQuery query);
} 