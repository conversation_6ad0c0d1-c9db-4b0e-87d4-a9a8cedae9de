package com.sankuai.dzusergrowth.operation.domain.model.request;

import com.sankuai.dzusergrowth.operation.api.enums.AnnotationTaskTypeEnum;
import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationConfigDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 标注任务更新请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationTaskUpdateRequest {
    
    /**
     * 任务ID（必填）
     */
    private Long taskId;
    
    /**
     * 任务名称（可选更新）
     */
    private String taskName;
    
    /**
     * 标注类型（可选更新）
     */
    private AnnotationTaskTypeEnum annotationType;
    
    /**
     * 标注配置（可选更新）
     */
    private List<AnnotationConfigDO> annotationConfig;
    
    /**
     * 展示选用的列（可选更新）
     */
    private List<Long> showColumn;
    
    /**
     * 任务标注人员（可选更新）
     */
    private String annotator;
    
    /**
     * 任务描述（可选更新）
     */
    private String description;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 验证更新请求参数有效性
     */
    public boolean isValid() {
        // 必填字段验证
        if (taskId == null) {
            return false;
        }
        
        if (updaterId == null) {
            return false;
        }
        
        // 如果提供了任务名称，需要验证格式
        if (taskName != null && taskName.trim().isEmpty()) {
            return false;
        }

        return true;
    }
    
    /**
     * 检查是否有实际要更新的字段
     */
    public boolean hasFieldsToUpdate() {
        return taskName != null || 
               annotationType != null || 
               annotationConfig != null || 
               (showColumn != null && !showColumn.isEmpty()) || 
               annotator != null || 
               description != null;
    }
    
    /**
     * 验证任务名称格式（如果提供）
     */
    public boolean hasValidTaskName() {
        return taskName == null || (!taskName.trim().isEmpty() && taskName.length() <= 100);
    }
    
    /**
     * 验证标注配置的完整性（如果提供）
     */
    public boolean hasValidAnnotationConfig() {
        if (annotationConfig == null) {
            return true; // 不更新配置时认为有效
        }
        
        if (annotationConfig.isEmpty()) {
            return false; // 如果提供了配置但为空，则无效
        }
        
        // 检查是否有重复的标签ID
        long distinctLabelCount = annotationConfig.stream()
                .map(AnnotationConfigDO::getColumnId)
                .distinct()
                .count();
                
        return distinctLabelCount == annotationConfig.size();
    }


    /**
     * 获取taskId
     *
     * @return taskId
     */
    public Long getTaskId() {
        return taskId;
    }

    /**
     * 获取taskName
     *
     * @return taskName
     */
    public String getTaskName() {
        return taskName;
    }

    /**
     * 获取annotationType
     *
     * @return annotationType
     */
    public AnnotationTaskTypeEnum getAnnotationType() {
        return annotationType;
    }

    /**
     * 获取annotationConfig
     *
     * @return annotationConfig
     */
    public List<AnnotationConfigDO> getAnnotationConfig() {
        return annotationConfig;
    }

    /**
     * 获取showColumn
     *
     * @return showColumn
     */
    public List<Long> getShowColumn() {
        return showColumn;
    }

    /**
     * 获取annotator
     *
     * @return annotator
     */
    public String getAnnotator() {
        return annotator;
    }

    /**
     * 获取description
     *
     * @return description
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取updaterId
     *
     * @return updaterId
     */
    public Long getUpdaterId() {
        return updaterId;
    }

    /**
     * 设置taskId
     *
     * @param taskId taskId
     */
    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    /**
     * 设置taskName
     *
     * @param taskName taskName
     */
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    /**
     * 设置annotationType
     *
     * @param annotationType annotationType
     */
    public void setAnnotationType(AnnotationTaskTypeEnum annotationType) {
        this.annotationType = annotationType;
    }

    /**
     * 设置annotationConfig
     *
     * @param annotationConfig annotationConfig
     */
    public void setAnnotationConfig(List<AnnotationConfigDO> annotationConfig) {
        this.annotationConfig = annotationConfig;
    }

    /**
     * 设置showColumn
     *
     * @param showColumn showColumn
     */
    public void setShowColumn(List<Long> showColumn) {
        this.showColumn = showColumn;
    }

    /**
     * 设置annotator
     *
     * @param annotator annotator
     */
    public void setAnnotator(String annotator) {
        this.annotator = annotator;
    }

    /**
     * 设置description
     *
     * @param description description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 设置updaterId
     *
     * @param updaterId updaterId
     */
    public void setUpdaterId(Long updaterId) {
        this.updaterId = updaterId;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static AnnotationTaskUpdateRequestBuilder builder() {
        return new AnnotationTaskUpdateRequestBuilder();
    }
    
    /**
     * Builder类
     */
    public static class AnnotationTaskUpdateRequestBuilder {
        private Long taskId;
        private String taskName;
        private AnnotationTaskTypeEnum annotationType;
        private List<AnnotationConfigDO> annotationConfig;
        private List<Long> showColumn;
        private String annotator;
        private String description;
        private Long updaterId;
        
        public AnnotationTaskUpdateRequestBuilder taskId(Long taskId) {
            this.taskId = taskId;
            return this;
        }
        public AnnotationTaskUpdateRequestBuilder taskName(String taskName) {
            this.taskName = taskName;
            return this;
        }
        public AnnotationTaskUpdateRequestBuilder annotationType(AnnotationTaskTypeEnum annotationType) {
            this.annotationType = annotationType;
            return this;
        }
        public AnnotationTaskUpdateRequestBuilder annotationConfig(List<AnnotationConfigDO> annotationConfig) {
            this.annotationConfig = annotationConfig;
            return this;
        }
        public AnnotationTaskUpdateRequestBuilder showColumn(List<Long> showColumn) {
            this.showColumn = showColumn;
            return this;
        }
        public AnnotationTaskUpdateRequestBuilder annotator(String annotator) {
            this.annotator = annotator;
            return this;
        }
        public AnnotationTaskUpdateRequestBuilder description(String description) {
            this.description = description;
            return this;
        }
        public AnnotationTaskUpdateRequestBuilder updaterId(Long updaterId) {
            this.updaterId = updaterId;
            return this;
        }
        
        public AnnotationTaskUpdateRequest build() {
            return new AnnotationTaskUpdateRequest(taskId, taskName, annotationType, annotationConfig, showColumn, annotator, description, updaterId);
        }
    }
} 