# 用户增长运营平台 - 领域层

## 概述

本模块是用户增长运营平台的领域层(Domain Layer)，按照DDD(领域驱动设计)的理念，将业务逻辑划分为三个核心领域：

1. **数据集域** - 负责数据集、数据条目和数据生成任务的管理
2. **标注域** - 负责标注任务、标注标签和标注数据的管理  
3. **评测域** - 负责评测任务和评测结果的管理

## 领域模型

### 数据集域 (Dataset Domain)

**核心实体:**
- `DatasetDO` - 数据集实体
- `DatasetColumnInfoDO` - 数据集列信息实体
- `DataItemDO` - 数据条目实体
- `DataGenerationTaskDO` - 数据生成任务实体

**领域服务:**
- `DatasetDomainService` - 数据集域服务接口

**主要功能:**
- 数据集的创建、查询、更新、删除
- 数据集列信息的管理
- 数据条目的批量操作和查询
- 数据生成任务的生命周期管理

### 标注域 (Annotation Domain)

**核心实体:**
- `AnnotationTaskDO` - 标注任务实体
- `AnnotationLabelDO` - 标注标签实体
- `AnnotationDataItemDO` - 标注数据条目实体

**领域服务:**
- `AnnotationDomainService` - 标注域服务接口

**主要功能:**
- 标注任务的创建和管理
- 标注标签的配置和维护
- 标注数据条目的状态跟踪
- 批量标注操作支持

### 评测域 (Evaluation Domain)

**核心实体:**
- `EvaluationTaskDO` - 评测任务实体

**领域服务:**
- `EvaluationDomainService` - 评测域服务接口

**主要功能:**
- 评测任务的创建和执行
- 评测结果的生成和管理
- 评测报告的导出
- 评测指标的统计分析

## 设计原则

### 1. 代码规范
- 严格遵循阿里巴巴Java开发规范
- 统一的命名规范和代码风格
- 完善的JavaDoc文档

### 2. 架构设计
- 高内聚、低耦合的模块设计
- 基于SOLID原则的接口设计
- 领域逻辑与技术实现分离

### 3. 业务验证
- 实体级别的业务规则验证
- 状态机模式管理任务生命周期
- 丰富的业务状态查询方法

### 4. 扩展性
- 接口导向的服务设计
- 支持未来功能扩展
- 可插拔的实现机制

## 技术栈

- **Java 8+** - 基础开发语言
- **Lombok** - 减少样板代码
- **Spring Framework** - 依赖注入和AOP支持

## 目录结构

```
src/main/java/com/sankuai/dzusergrowth/operation/domain/
├── model/                          # 领域模型
│   ├── datasetPO/                   # 数据集域实体
│   │   ├── DatasetDO.java
│   │   ├── DatasetColumnInfoDO.java
│   │   ├── DataItemDO.java
│   │   └── DataGenerationTaskDO.java
│   ├── annotation/                # 标注域实体
│   │   ├── AnnotationTaskDO.java
│   │   ├── AnnotationLabelDO.java
│   │   └── AnnotationDataItemDO.java
│   └── evaluation/                # 评测域实体
│       └── EvaluationTaskDO.java
└── service/                       # 领域服务
    ├── DatasetDomainService.java
    ├── AnnotationDomainService.java
    └── EvaluationDomainService.java
```

## 开发指南

### 实体设计
- 所有DO实体都包含基础的审计字段(创建时间、更新时间、创建人、更新人)
- 提供业务验证方法，如`isValidName()`、`isValidStatus()`等
- 包含状态查询方法，如`isPending()`、`isCompleted()`等
- 使用Lombok注解减少样板代码

### 服务接口设计
- 以实体的增删改查为主要功能
- 提供批量操作支持
- 包含业务统计方法
- 支持分页查询
- 丰富的查询条件支持

### 业务规则
- 在实体中封装业务验证逻辑
- 在服务接口中定义业务操作规范
- 通过状态机管理任务生命周期
- 支持软删除机制

## 后续规划

1. **仓储接口定义** - 定义与基础设施层的交互接口
2. **领域事件** - 引入事件驱动架构支持
3. **值对象** - 提取复杂业务概念为值对象
4. **聚合根** - 完善聚合边界定义
5. **单元测试** - 完善领域逻辑的单元测试覆盖

---

*本文档生成于：2024年*
*开发者：AI Assistant* 