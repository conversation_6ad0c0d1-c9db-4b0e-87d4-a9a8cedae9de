package com.sankuai.dzusergrowth.operation.starter.gateway;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dzusergrowth.operation.api.common.PageResult;
import com.sankuai.dzusergrowth.operation.api.request.*;
import com.sankuai.dzusergrowth.operation.api.response.DatasetDTO;
import com.sankuai.dzusergrowth.operation.api.response.DatasetDetailResponse;
import com.sankuai.dzusergrowth.operation.api.service.DatasetGatewayService;
import com.sankuai.dzusergrowth.operation.application.service.DatasetAppService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 数据集网关层
 * 暴露RPC接口供外部调用
 *
 * <AUTHOR> Assistant
 */
@MdpPigeonServer
public class DatasetGatewayServiceImpl implements DatasetGatewayService {
    
    @Autowired
    private DatasetAppService datasetAppService;
    
    /**
     * 获取数据集列表
     * ElinkID: 7371
     * Path: /api/dzusergrowth/agentbackend/dataset/list
     *
     * @param request 查询请求
     * @return 分页数据集列表
     */
    public PageResult<DatasetDTO> list(DatasetListRequest request) {
        return datasetAppService.list(request);
    }
    
    /**
     * 新增数据集
     * ElinkID: 7374
     * Path: /api/dzusergrowth/agentbackend/dataset/create
     *
     * @param request 创建请求
     * @return 数据集ID
     */
    public String create(DatasetCreateRequest request) {
        return datasetAppService.create(request);
    }
    
    /**
     * 编辑数据集名称描述
     * ElinkID: 7372
     * Path: /api/dzusergrowth/agentbackend/dataset/update
     *
     * @param request 更新请求
     * @return 是否成功
     */
    public Boolean update(DatasetUpdateRequest request) {
        return datasetAppService.update(request);
    }
    
    /**
     * 获取数据集详情
     * ElinkID: 7373
     * Path: /api/dzusergrowth/agentbackend/dataset/detail
     *
     * @param request 详情查询请求
     * @return 数据集详情
     */
    public DatasetDetailResponse detail(DatasetDetailRequest request) {
        return datasetAppService.detail(request);
    }
} 