package com.sankuai.dzusergrowth.operation.starter.controller;

import com.sankuai.dzusergrowth.operation.api.common.PageResult;
import com.sankuai.dzusergrowth.operation.api.request.OfflineCodeActivityRecordQueryRequest;
import com.sankuai.dzusergrowth.operation.api.response.OfflineCodeActivityRecordDTO;
import com.sankuai.dzusergrowth.operation.application.service.OfflineCodeActivityRecordAppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/dzusergrowth/operation/offlinecode")
public class OfflineCodeActivityRecordController {

    @Autowired
    private OfflineCodeActivityRecordAppService offlineCodeActivityRecordAppService;

    @GetMapping("/activityRecord/query")
    public PageResult<OfflineCodeActivityRecordDTO> queryActivityRecords(OfflineCodeActivityRecordQueryRequest request) {
        return offlineCodeActivityRecordAppService.queryActivityRecords(request);
        
    }
}
