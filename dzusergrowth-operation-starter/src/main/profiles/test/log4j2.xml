<?xml version="1.0" encoding="UTF-8"?>
<configuration status="info">
    <appenders>
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %t %-5level %logger{30}.%method - %msg%n"/>
        </Console>
        <!--默认按天&按512M文件大小切分日志，默认最多保留30个日志文件，非阻塞模式-->
        <XMDFile name="infoAppender" fileName="info.log" sizeBasedTriggeringSize="512M" rolloverMax="30">
            <Filters>
                <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </XMDFile>
        <XMDFile name="warnAppender" fileName="warn.log" sizeBasedTriggeringSize="512M" rolloverMax="30">
            <Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </XMDFile>
        <XMDFile name="errorAppender" fileName="error.log" sizeBasedTriggeringSize="512M" rolloverMax="30">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
        </XMDFile>

        <!--异步日志上报远程配置示例-->
        <!-- 如果期望将上报日志从 异步 改为 同步，容忍同步时延，避免丢失，请修改blocking=true参数 -->
        <AsyncScribe name="AsyncScribeAppender" blocking="false">
            <!-- 在指定日志名方面，scribeCategory 和 appkey 两者至少存在一种，且 scribeCategory 高于 appkey。-->
            <!--该标记表示是否开启丢失率检测，true为开启，false为不开启，默认为false -->
            <Property name="checkLoss">true</Property>
            <LcLayout/>
        </AsyncScribe>

        <CatAppender name="catAppender"/>
        <MDPTraceAppender name="mdpTrace"/>
    </appenders>

    <loggers>

        <root level="debug">
            <appender-ref ref="infoAppender"/>
            <appender-ref ref="warnAppender"/>
            <appender-ref ref="errorAppender"/>
            <appender-ref ref="Console"/>
            <appender-ref ref="catAppender"/>
            <appender-ref ref="mdpTrace"/>
            <appender-ref ref="AsyncScribeAppender"/>
        </root>
        <logger name="com.meituan.hotel" level="warn"/>
    </loggers>
</configuration>