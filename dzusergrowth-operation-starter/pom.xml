<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.dzusergrowth</groupId>
        <artifactId>dzusergrowth-operation</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>dzusergrowth-operation-starter</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>dzusergrowth-operation-starter</name>

    <dependencies>
        <!-- Project module -->
        <dependency>
            <groupId>com.sankuai.dzusergrowth</groupId>
            <artifactId>dzusergrowth-operation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzusergrowth</groupId>
            <artifactId>dzusergrowth-operation-application</artifactId>
        </dependency>

        <!-- region MDP 脚手架生成代码统计埋点 内容为空，不引入其他依赖，请勿删除 -->
        <!-- MDP LA Initializer -->
        <dependency>
            <groupId>com.sankuai.mdp</groupId>
            <artifactId>mdp-boot-initializr-mdp</artifactId>
            <version>1.0.0</version>
        </dependency>
        <!-- endregion MDP 脚手架生成代码统计埋点 -->

        <!-- Mdp Boot Starter -->
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter</artifactId>
        </dependency>

        <!-- Mdp Boot Components -->
        <dependency>
            <groupId>com.meituan.mdp.component</groupId>
            <artifactId>swagger-analysis-core</artifactId>
            <scope>compile</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.component</groupId>
            <artifactId>mdp-doc</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 第三方依赖 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.oceanus.registry</groupId>
            <artifactId>oceanus-registry</artifactId>
            <version>1.1.15</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-mybatis-generator-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>swagger-analysis-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-doc-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-graphql-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-statistics-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>register</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>make</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
