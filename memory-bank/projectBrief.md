# 项目概述

## 项目基本信息

- **项目名称**: dzusergrowth-operation (用户增长运营平台)
- **AppKey**: com.sankuai.dzusergrowth.operation
- **架构模式**: DDD (领域驱动设计)
- **技术栈**: Java + Spring + MyBatis + Maven

## 项目目标

基于美团用户增长团队的运营需求，构建一个AI驱动的数据标注和管理平台，支持：

1. **智能数据标注**: 提供高效的数据标注工具和流程
2. **数据集管理**: 统一管理各类业务数据集
3. **评估体系**: 构建完整的数据质量评估机制
4. **运营支撑**: 为用户增长业务提供数据支撑

## 核心业务领域

### 1. 数据标注 (Annotation)
- 标注任务管理
- 标注数据项处理
- 标注标签体系

### 2. 数据集管理 (Dataset)
- 数据集创建和配置
- 数据列定义和管理
- 数据项存储和检索

### 3. 评估系统 (Evaluation)
- 评估任务创建
- 评估指标配置
- 评估结果分析

## 技术架构

```mermaid
graph TD
    A[dzusergrowth-operation-starter<br/>启动层] --> B[dzusergrowth-operation-api<br/>接口层]
    A --> C[dzusergrowth-operation-application<br/>应用层]
    C --> D[dzusergrowth-operation-domain<br/>领域层]
    C --> E[dzusergrowth-operation-infrastructure<br/>基础设施层]
    D --> E
```

## 项目范围

- **包含**: AI数据管理、标注工具、评估体系
- **不包含**: 具体的AI模型训练、推理服务
- **边界**: 专注于数据处理和管理环节，与上游数据源和下游AI服务解耦

## 更新时间

2025-07-01 15:21:25 