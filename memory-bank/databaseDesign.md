# 数据库设计

## 数据库概览

基于 MyBatis Mapper 文件分析，系统包含7个核心数据表，主要围绕AI数据管理、标注和评估功能设计。

## 表结构详情

### 1. ai_admin_annotation_data_item (标注数据项表)

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | BIGINT | 主键 | PRIMARY KEY |
| dataset_id | BIGINT | 数据集ID | |
| data_unique_key | VARCHAR | 数据唯一键-标识: 比如TraceID，活动ID等 | |
| task_id | BIGINT | 任务ID | |
| status | INTEGER | 状态 | |
| add_time | TIMESTAMP | 添加时间 | |
| update_time | TIMESTAMP | 更新时间 | |
| creator_id | BIGINT | 创建人ID | |
| updater_id | BIGINT | 更新人ID | |

### 2. ai_admin_dataset (数据集表)

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | BIGINT | 主键 | PRIMARY KEY |
| dataset_type | INTEGER | 数据集类型 | |
| data_granularity | INTEGER | 数据粒度 | |
| name | VARCHAR | 数据集名称 | |
| generate_type | INTEGER | 生成类型 | |
| generate_config | CHAR | 生成配置 | |
| task_status | INTEGER | 任务状态 | |
| description | LONGVARCHAR | 描述信息 | BLOB |
| add_time | TIMESTAMP | 添加时间 | |
| update_time | TIMESTAMP | 更新时间 | |
| creator_id | BIGINT | 创建人ID | |
| updater_id | BIGINT | 更新人ID | |

### 3. 其他核心表

基于文件结构，系统还包含以下表（详细字段需进一步分析）：

- **ai_admin_annotation_label**: 标注标签表
- **ai_admin_annotation_task**: 标注任务表  
- **ai_admin_data_item**: 数据项表
- **ai_admin_dataset_column_info**: 数据集列信息表
- **ai_admin_evaluation_task**: 评估任务表

## 实体关系图

```mermaid
erDiagram
    ai_admin_dataset ||--o{ ai_admin_annotation_data_item : "dataset_id"
    ai_admin_annotation_task ||--o{ ai_admin_annotation_data_item : "task_id"
    ai_admin_dataset ||--o{ ai_admin_dataset_column_info : "dataset_id"
    ai_admin_dataset ||--o{ ai_admin_data_item : "dataset_id"
    ai_admin_annotation_task ||--o{ ai_admin_annotation_label : "task_id"
    ai_admin_evaluation_task ||--o{ ai_admin_dataset : "related"
    
    ai_admin_dataset {
        bigint id PK
        integer dataset_type
        integer data_granularity
        varchar name
        integer generate_type
        char generate_config
        integer task_status
        longvarchar description
        timestamp add_time
        timestamp update_time
        bigint creator_id
        bigint updater_id
    }
    
    ai_admin_annotation_data_item {
        bigint id PK
        bigint dataset_id FK
        varchar data_unique_key
        bigint task_id FK
        integer status
        timestamp add_time
        timestamp update_time
        bigint creator_id
        bigint updater_id
    }
    
    ai_admin_annotation_task {
        bigint id PK
        varchar name
        text description
        integer status
        timestamp add_time
        timestamp update_time
        bigint creator_id
        bigint updater_id
    }
```

## 设计原则

1. **统一字段**: 所有表都包含标准的审计字段 (add_time, update_time, creator_id, updater_id)
2. **主键策略**: 使用 BIGINT 类型的自增主键
3. **外键关联**: 通过 dataset_id 和 task_id 建立表间关联
4. **状态管理**: 使用 INTEGER 类型的 status 字段管理实体状态
5. **扩展性**: 使用配置字段 (generate_config) 支持灵活配置

## 更新时间

2025-07-01 15:21:25 