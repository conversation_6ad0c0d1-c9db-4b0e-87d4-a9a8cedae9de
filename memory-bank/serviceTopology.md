# 服务拓扑

## 服务基本信息

- **AppKey**: com.sankuai.dzusergrowth.operation
- **服务类型**: 用户增长运营平台
- **架构模式**: DDD + Spring Boot

## 服务依赖分析

### 上游依赖

根据 Raptor 查询结果，当前系统没有明确的上游服务依赖，这表明：
- 可能是一个相对独立的系统
- 或者是新建系统，依赖关系尚未建立
- 数据输入可能通过文件上传或手动录入

### 下游依赖

同样没有检测到下游服务依赖，说明：
- 系统主要提供内部管理功能
- 可能通过数据库或文件形式输出结果
- 未与其他系统进行实时集成

## 内部服务结构

基于代码扫描结果：

```mermaid
graph TD
    A[OfflineCodeActivityRecordController<br/>离线代码活动记录控制器] --> B[OfflineCodeActivityRecordAppService<br/>应用服务]
    B --> C[OfflineCodeActivityRecordAppServiceImpl<br/>应用服务实现]
    
    C --> D[AnnotationDomainService<br/>标注领域服务]
    C --> E[DatasetDomainService<br/>数据集领域服务] 
    C --> F[EvaluationDomainService<br/>评估领域服务]
    
    D --> G[AnnotationDataItemRepository<br/>标注数据项仓储]
    D --> H[AnnotationLabelRepository<br/>标注标签仓储]
    D --> I[AnnotationTaskRepository<br/>标注任务仓储]
    
    E --> J[DatasetRepository<br/>数据集仓储]
    E --> K[DataItemRepository<br/>数据项仓储]
    E --> L[DatasetColumnInfoRepository<br/>数据集列信息仓储]
    
    F --> M[EvaluationTaskRepository<br/>评估任务仓储]
```

## API 端点

### 已识别端点

- **OfflineCodeActivityRecordController**: 处理离线代码活动记录相关请求

### 预期端点（基于领域模型）

1. **标注管理 API**
   - 标注任务CRUD
   - 标注数据管理
   - 标注标签管理

2. **数据集管理 API**
   - 数据集CRUD
   - 数据项管理
   - 数据列配置

3. **评估系统 API**
   - 评估任务管理
   - 评估结果查询

## 技术栈分析

- **Web框架**: Spring Boot
- **数据访问**: MyBatis
- **数据库**: MySQL (推测)
- **架构模式**: DDD (Domain-Driven Design)

## 流量特征分析

由于 Raptor 查询暂未返回有效流量数据，推测可能原因：
1. 系统刚上线，流量较少
2. 主要为内部管理系统，访问量较低
3. 可能处于开发或测试阶段

## 部署架构

```mermaid
graph TD
    A[Load Balancer] --> B[dzusergrowth-operation<br/>应用实例]
    B --> C[MySQL Database<br/>AI管理数据库]
    B --> D[File Storage<br/>数据文件存储]
```

## 监控建议

1. **应用监控**: 关注关键业务接口的响应时间和成功率
2. **数据库监控**: 监控数据库连接数和查询性能
3. **业务监控**: 关注数据标注完成率和数据集处理效率

## 更新时间

2025-07-01 15:21:25 