package com.sankuai.dzusergrowth.operation.infrastructure.dal.po;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: ai_admin_dataset_column_info
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DatasetColumnInfoPO {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: dataset_id
     *   说明: 数据集id
     */
    private Long datasetId;

    /**
     *   字段: name
     *   说明: 列名
     */
    private String name;

    /**
     *   字段: display_name
     *   说明: 列展示名
     */
    private String displayName;

    /**
     *   字段: data_type
     *   说明: 数据类型: 1-string(目前先解析string); 2-long; 3-float
     */
    private Integer dataType;

    /**
     *   字段: column_type
     *   说明: 列类型: 1-生成数据; 2-标注结果; 3-统计指标; 4-大模型评测指标
     */
    private Integer columnType;

    /**
     *   字段: column_config
     *   说明: 列配置: JSON
     */
    private String columnConfig;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: creator_id
     *   说明: 创建人ID
     */
    private Long creatorId;

    /**
     *   字段: updater_id
     *   说明: 更新人ID
     */
    private Long updaterId;
}