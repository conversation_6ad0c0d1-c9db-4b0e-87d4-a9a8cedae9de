package com.sankuai.dzusergrowth.operation.infrastructure.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DatasetPOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public DatasetPOExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public DatasetPOExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public DatasetPOExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public DatasetPOExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDatasetTypeIsNull() {
            addCriterion("dataset_type is null");
            return (Criteria) this;
        }

        public Criteria andDatasetTypeIsNotNull() {
            addCriterion("dataset_type is not null");
            return (Criteria) this;
        }

        public Criteria andDatasetTypeEqualTo(Integer value) {
            addCriterion("dataset_type =", value, "datasetType");
            return (Criteria) this;
        }

        public Criteria andDatasetTypeNotEqualTo(Integer value) {
            addCriterion("dataset_type <>", value, "datasetType");
            return (Criteria) this;
        }

        public Criteria andDatasetTypeGreaterThan(Integer value) {
            addCriterion("dataset_type >", value, "datasetType");
            return (Criteria) this;
        }

        public Criteria andDatasetTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("dataset_type >=", value, "datasetType");
            return (Criteria) this;
        }

        public Criteria andDatasetTypeLessThan(Integer value) {
            addCriterion("dataset_type <", value, "datasetType");
            return (Criteria) this;
        }

        public Criteria andDatasetTypeLessThanOrEqualTo(Integer value) {
            addCriterion("dataset_type <=", value, "datasetType");
            return (Criteria) this;
        }

        public Criteria andDatasetTypeIn(List<Integer> values) {
            addCriterion("dataset_type in", values, "datasetType");
            return (Criteria) this;
        }

        public Criteria andDatasetTypeNotIn(List<Integer> values) {
            addCriterion("dataset_type not in", values, "datasetType");
            return (Criteria) this;
        }

        public Criteria andDatasetTypeBetween(Integer value1, Integer value2) {
            addCriterion("dataset_type between", value1, value2, "datasetType");
            return (Criteria) this;
        }

        public Criteria andDatasetTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("dataset_type not between", value1, value2, "datasetType");
            return (Criteria) this;
        }

        public Criteria andDataGranularityIsNull() {
            addCriterion("data_granularity is null");
            return (Criteria) this;
        }

        public Criteria andDataGranularityIsNotNull() {
            addCriterion("data_granularity is not null");
            return (Criteria) this;
        }

        public Criteria andDataGranularityEqualTo(Integer value) {
            addCriterion("data_granularity =", value, "dataGranularity");
            return (Criteria) this;
        }

        public Criteria andDataGranularityNotEqualTo(Integer value) {
            addCriterion("data_granularity <>", value, "dataGranularity");
            return (Criteria) this;
        }

        public Criteria andDataGranularityGreaterThan(Integer value) {
            addCriterion("data_granularity >", value, "dataGranularity");
            return (Criteria) this;
        }

        public Criteria andDataGranularityGreaterThanOrEqualTo(Integer value) {
            addCriterion("data_granularity >=", value, "dataGranularity");
            return (Criteria) this;
        }

        public Criteria andDataGranularityLessThan(Integer value) {
            addCriterion("data_granularity <", value, "dataGranularity");
            return (Criteria) this;
        }

        public Criteria andDataGranularityLessThanOrEqualTo(Integer value) {
            addCriterion("data_granularity <=", value, "dataGranularity");
            return (Criteria) this;
        }

        public Criteria andDataGranularityIn(List<Integer> values) {
            addCriterion("data_granularity in", values, "dataGranularity");
            return (Criteria) this;
        }

        public Criteria andDataGranularityNotIn(List<Integer> values) {
            addCriterion("data_granularity not in", values, "dataGranularity");
            return (Criteria) this;
        }

        public Criteria andDataGranularityBetween(Integer value1, Integer value2) {
            addCriterion("data_granularity between", value1, value2, "dataGranularity");
            return (Criteria) this;
        }

        public Criteria andDataGranularityNotBetween(Integer value1, Integer value2) {
            addCriterion("data_granularity not between", value1, value2, "dataGranularity");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andGenerateTypeIsNull() {
            addCriterion("generate_type is null");
            return (Criteria) this;
        }

        public Criteria andGenerateTypeIsNotNull() {
            addCriterion("generate_type is not null");
            return (Criteria) this;
        }

        public Criteria andGenerateTypeEqualTo(Integer value) {
            addCriterion("generate_type =", value, "generateType");
            return (Criteria) this;
        }

        public Criteria andGenerateTypeNotEqualTo(Integer value) {
            addCriterion("generate_type <>", value, "generateType");
            return (Criteria) this;
        }

        public Criteria andGenerateTypeGreaterThan(Integer value) {
            addCriterion("generate_type >", value, "generateType");
            return (Criteria) this;
        }

        public Criteria andGenerateTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("generate_type >=", value, "generateType");
            return (Criteria) this;
        }

        public Criteria andGenerateTypeLessThan(Integer value) {
            addCriterion("generate_type <", value, "generateType");
            return (Criteria) this;
        }

        public Criteria andGenerateTypeLessThanOrEqualTo(Integer value) {
            addCriterion("generate_type <=", value, "generateType");
            return (Criteria) this;
        }

        public Criteria andGenerateTypeIn(List<Integer> values) {
            addCriterion("generate_type in", values, "generateType");
            return (Criteria) this;
        }

        public Criteria andGenerateTypeNotIn(List<Integer> values) {
            addCriterion("generate_type not in", values, "generateType");
            return (Criteria) this;
        }

        public Criteria andGenerateTypeBetween(Integer value1, Integer value2) {
            addCriterion("generate_type between", value1, value2, "generateType");
            return (Criteria) this;
        }

        public Criteria andGenerateTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("generate_type not between", value1, value2, "generateType");
            return (Criteria) this;
        }

        public Criteria andGenerateConfigIsNull() {
            addCriterion("generate_config is null");
            return (Criteria) this;
        }

        public Criteria andGenerateConfigIsNotNull() {
            addCriterion("generate_config is not null");
            return (Criteria) this;
        }

        public Criteria andGenerateConfigEqualTo(String value) {
            addCriterion("generate_config =", value, "generateConfig");
            return (Criteria) this;
        }

        public Criteria andGenerateConfigNotEqualTo(String value) {
            addCriterion("generate_config <>", value, "generateConfig");
            return (Criteria) this;
        }

        public Criteria andGenerateConfigGreaterThan(String value) {
            addCriterion("generate_config >", value, "generateConfig");
            return (Criteria) this;
        }

        public Criteria andGenerateConfigGreaterThanOrEqualTo(String value) {
            addCriterion("generate_config >=", value, "generateConfig");
            return (Criteria) this;
        }

        public Criteria andGenerateConfigLessThan(String value) {
            addCriterion("generate_config <", value, "generateConfig");
            return (Criteria) this;
        }

        public Criteria andGenerateConfigLessThanOrEqualTo(String value) {
            addCriterion("generate_config <=", value, "generateConfig");
            return (Criteria) this;
        }

        public Criteria andGenerateConfigLike(String value) {
            addCriterion("generate_config like", value, "generateConfig");
            return (Criteria) this;
        }

        public Criteria andGenerateConfigNotLike(String value) {
            addCriterion("generate_config not like", value, "generateConfig");
            return (Criteria) this;
        }

        public Criteria andGenerateConfigIn(List<String> values) {
            addCriterion("generate_config in", values, "generateConfig");
            return (Criteria) this;
        }

        public Criteria andGenerateConfigNotIn(List<String> values) {
            addCriterion("generate_config not in", values, "generateConfig");
            return (Criteria) this;
        }

        public Criteria andGenerateConfigBetween(String value1, String value2) {
            addCriterion("generate_config between", value1, value2, "generateConfig");
            return (Criteria) this;
        }

        public Criteria andGenerateConfigNotBetween(String value1, String value2) {
            addCriterion("generate_config not between", value1, value2, "generateConfig");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIsNull() {
            addCriterion("task_status is null");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIsNotNull() {
            addCriterion("task_status is not null");
            return (Criteria) this;
        }

        public Criteria andTaskStatusEqualTo(Integer value) {
            addCriterion("task_status =", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotEqualTo(Integer value) {
            addCriterion("task_status <>", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusGreaterThan(Integer value) {
            addCriterion("task_status >", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("task_status >=", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusLessThan(Integer value) {
            addCriterion("task_status <", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusLessThanOrEqualTo(Integer value) {
            addCriterion("task_status <=", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIn(List<Integer> values) {
            addCriterion("task_status in", values, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotIn(List<Integer> values) {
            addCriterion("task_status not in", values, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusBetween(Integer value1, Integer value2) {
            addCriterion("task_status between", value1, value2, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("task_status not between", value1, value2, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIsNull() {
            addCriterion("creator_id is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIsNotNull() {
            addCriterion("creator_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorIdEqualTo(Long value) {
            addCriterion("creator_id =", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotEqualTo(Long value) {
            addCriterion("creator_id <>", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdGreaterThan(Long value) {
            addCriterion("creator_id >", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdGreaterThanOrEqualTo(Long value) {
            addCriterion("creator_id >=", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdLessThan(Long value) {
            addCriterion("creator_id <", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdLessThanOrEqualTo(Long value) {
            addCriterion("creator_id <=", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIn(List<Long> values) {
            addCriterion("creator_id in", values, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotIn(List<Long> values) {
            addCriterion("creator_id not in", values, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdBetween(Long value1, Long value2) {
            addCriterion("creator_id between", value1, value2, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotBetween(Long value1, Long value2) {
            addCriterion("creator_id not between", value1, value2, "creatorId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdIsNull() {
            addCriterion("updater_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdIsNotNull() {
            addCriterion("updater_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdEqualTo(Long value) {
            addCriterion("updater_id =", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdNotEqualTo(Long value) {
            addCriterion("updater_id <>", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdGreaterThan(Long value) {
            addCriterion("updater_id >", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdGreaterThanOrEqualTo(Long value) {
            addCriterion("updater_id >=", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdLessThan(Long value) {
            addCriterion("updater_id <", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdLessThanOrEqualTo(Long value) {
            addCriterion("updater_id <=", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdIn(List<Long> values) {
            addCriterion("updater_id in", values, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdNotIn(List<Long> values) {
            addCriterion("updater_id not in", values, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdBetween(Long value1, Long value2) {
            addCriterion("updater_id between", value1, value2, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdNotBetween(Long value1, Long value2) {
            addCriterion("updater_id not between", value1, value2, "updaterId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}