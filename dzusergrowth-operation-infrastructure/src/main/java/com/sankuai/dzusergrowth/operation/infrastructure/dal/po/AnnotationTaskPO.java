package com.sankuai.dzusergrowth.operation.infrastructure.dal.po;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: ai_admin_annotation_task
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationTaskPO {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: annotation_dataset_id
     *   说明: 标注数据的数据集ID
     */
    private Long annotationDatasetId;

    /**
     *   字段: task_name
     *   说明: 任务名称
     */
    private String taskName;

    /**
     *   字段: annotation_type
     *   说明: 标注类型: 1-人工标注; 2-自动标注
     */
    private Integer annotationType;

    /**
     *   字段: annotation_config
     *   说明: 标注配置: JSON
     */
    private String annotationConfig;

    /**
     *   字段: show_column
     *   说明: 展示选用的列
     */
    private String showColumn;

    /**
     *   字段: annotator
     *   说明: 任务标注人员id
     */
    private String annotator;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: creator_id
     *   说明: 创建人ID
     */
    private Long creatorId;

    /**
     *   字段: updater_id
     *   说明: 更新人ID
     */
    private Long updaterId;

    /**
     *   字段: description
     *   说明: 任务描述
     */
    private String description;
}