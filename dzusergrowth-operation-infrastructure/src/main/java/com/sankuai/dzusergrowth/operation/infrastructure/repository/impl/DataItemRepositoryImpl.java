package com.sankuai.dzusergrowth.operation.infrastructure.repository.impl;

import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DataItemDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.DataItemQuery;
import com.sankuai.dzusergrowth.operation.domain.repository.DataItemRepository;
import com.sankuai.dzusergrowth.operation.infrastructure.converter.DataItemConverter;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DataItemPOExample;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.mapper.DataItemPOMapper;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DataItemPO;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据条目仓储实现类
 */
@Repository
public class DataItemRepositoryImpl implements DataItemRepository {

    @Resource
    private DataItemPOMapper dataItemMapper;

    /**
     * 创建新的数据条目
     *
     * @param dataItemDO 数据条目信息DO
     */
    @Override
    public void createDataItem(DataItemDO dataItemDO) {
        if (dataItemDO == null) {
            throw new IllegalArgumentException("数据条目对象不能为空");
        }
        
        // 根据DataItemDO构建DataItem PO对象
        DataItemPO dataItemPO = DataItemConverter.convertDOToPO(dataItemDO);
        // 保存数据条目到数据库
        dataItemMapper.insertSelective(dataItemPO);
        
        // 设置生成的ID
        dataItemDO.setId(dataItemPO.getId());
    }

    /**
     * 根据ID获取数据条目
     *
     * @param itemId 条目ID
     * @return 数据条目信息，如不存在则返回null
     */
    @Override
    public DataItemDO getDataItemById(Long itemId) {
        if (itemId == null) {
            return null;
        }
        
        DataItemPOExample example = new DataItemPOExample();
        example.createCriteria().andIdEqualTo(itemId);

        // 使用ZebraForceMasterHelper强制查询主库
        try {
            ZebraForceMasterHelper.forceMasterInLocalContext();
            List<DataItemPO> dataItemPOS = dataItemMapper.selectByExample(example);
            if (dataItemPOS == null || dataItemPOS.isEmpty()) {
                return null;
            }

            return DataItemConverter.convertPOToDO(dataItemPOS.get(0));
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
    }
    
    /**
     * 更新数据条目
     *
     * @param dataItemDO 数据条目DO
     */
    @Override
    public void updateDataItem(DataItemDO dataItemDO) {
        if (dataItemDO == null) {
            throw new IllegalArgumentException("数据条目对象不能为空");
        }
        
        if (dataItemDO.getId() == null) {
            throw new IllegalArgumentException("数据条目ID不能为空");
        }
        
        // 构建更新对象
        DataItemPO dataItemPO = DataItemConverter.convertDOToPO(dataItemDO);
        
        // 构建条件
        DataItemPOExample example = new DataItemPOExample();
        example.createCriteria().andIdEqualTo(dataItemDO.getId());
        
        // 执行更新
        int result = dataItemMapper.updateByExampleSelective(dataItemPO, example);
        
        if (result != 1) {
            throw new IllegalStateException("更新数据条目失败，条目ID: " + dataItemDO.getId() + "，可能条目不存在");
        }
    }
    
    /**
     * 软删除数据条目
     *
     * @param itemId 条目ID
     */
    @Override
    public void softDeleteDataItem(Long itemId) {
        if (itemId == null) {
            throw new IllegalArgumentException("数据条目ID不能为空");
        }
        
        // 先查询确认数据存在
        DataItemPOExample queryExample = new DataItemPOExample();
        queryExample.createCriteria().andIdEqualTo(itemId).andIsDeletedEqualTo(false);
        
        List<DataItemPO> existingItems = dataItemMapper.selectByExample(queryExample);
        if (CollectionUtils.isEmpty(existingItems)) {
            throw new IllegalStateException("软删除数据条目失败，条目ID: " + itemId + "，可能条目不存在或已删除");
        }
        
        // 构建软删除更新对象
        DataItemPO updateDataItemPO = new DataItemPO();
        updateDataItemPO.setIsDeleted(true);
        updateDataItemPO.setUpdateTime(new Date());
        
        // 构建更新条件
        DataItemPOExample updateExample = new DataItemPOExample();
        updateExample.createCriteria().andIdEqualTo(itemId).andIsDeletedEqualTo(false);
        
        // 执行软删除
        int result = dataItemMapper.updateByExampleSelective(updateDataItemPO, updateExample);
        
        if (result != 1) {
            throw new IllegalStateException("软删除数据条目失败，条目ID: " + itemId + "，可能条目不存在或已删除");
        }
    }
    
    /**
     * 批量查询数据条目
     *
     * @param query 数据条目查询条件
     * @return 数据条目DO列表
     */
    @Override
    public List<DataItemDO> queryDataItem(DataItemQuery query) {
        if (query == null) {
            throw new IllegalArgumentException("查询条件不能为空");
        }
        
        DataItemPOExample example = new DataItemPOExample();
        DataItemPOExample.Criteria criteria = example.createCriteria();
        
        // 软删除过滤：默认不包含已删除数据
        if (query.getIncludeDeleted() == null || !query.getIncludeDeleted()) {
            criteria.andIsDeletedEqualTo(false);
        }
        
        // 构建查询条件
        if (!CollectionUtils.isEmpty(query.getItemIds())) {
            criteria.andIdIn(query.getItemIds());
        }
        
        if (query.getDatasetId() != null) {
            criteria.andDatasetIdEqualTo(query.getDatasetId());
        }
        
        if (!CollectionUtils.isEmpty(query.getDatasetIds())) {
            criteria.andDatasetIdIn(query.getDatasetIds());
        }
        
        if (StringUtils.hasText(query.getDataUniqueKey())) {
            criteria.andDataUniqueKeyEqualTo(query.getDataUniqueKey());
        }
        
        if (query.getCreatorId() != null) {
            criteria.andCreatorIdEqualTo(query.getCreatorId());
        }
        
        if (query.getAddTimeStart() != null) {
            criteria.andAddTimeGreaterThanOrEqualTo(query.getAddTimeStart());
        }
        
        if (query.getAddTimeEnd() != null) {
            criteria.andAddTimeLessThanOrEqualTo(query.getAddTimeEnd());
        }
        
        // 设置排序
        if (StringUtils.hasText(query.getOrderBy())) {
            String orderClause = query.getOrderBy();
            if (StringUtils.hasText(query.getOrderDirection())) {
                orderClause += " " + query.getOrderDirection();
            }
            example.setOrderByClause(orderClause);
        } else {
            example.setOrderByClause("add_time DESC");
        }
        
        // 处理分页
        if (query.getPageSize() != null && query.getPageSize() > 0) {
            int pageNum = query.getPageNum() != null && query.getPageNum() > 0 ? query.getPageNum() : 1;
            int offset = (pageNum - 1) * query.getPageSize();
            example.setOffset(offset);
            example.setRows(query.getPageSize());
        }
        
        List<DataItemPO> dataItemPOS = dataItemMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(dataItemPOS)) {
            return Collections.emptyList();
        }
        
        return dataItemPOS.stream()
                .map(DataItemConverter::convertPOToDO)
                .collect(Collectors.toList());
    }
    
    /**
     * 统计符合条件的数据条目数量（用于分页查询）
     *
     * @param query 数据条目查询条件
     * @return 数据条目数量
     */
    @Override
    public long countDataItem(DataItemQuery query) {
        if (query == null) {
            throw new IllegalArgumentException("查询条件不能为空");
        }
        
        DataItemPOExample example = new DataItemPOExample();
        DataItemPOExample.Criteria criteria = example.createCriteria();
        
        // 软删除过滤：默认不包含已删除数据（与queryDataItem方法保持一致）
        if (query.getIncludeDeleted() == null || !query.getIncludeDeleted()) {
            criteria.andIsDeletedEqualTo(false);
        }
        
        // 构建查询条件（与queryDataItem方法保持一致，但不需要分页和排序）
        if (!CollectionUtils.isEmpty(query.getItemIds())) {
            criteria.andIdIn(query.getItemIds());
        }
        
        if (query.getDatasetId() != null) {
            criteria.andDatasetIdEqualTo(query.getDatasetId());
        }
        
        if (!CollectionUtils.isEmpty(query.getDatasetIds())) {
            criteria.andDatasetIdIn(query.getDatasetIds());
        }
        
        if (StringUtils.hasText(query.getDataUniqueKey())) {
            criteria.andDataUniqueKeyEqualTo(query.getDataUniqueKey());
        }
        
        if (query.getCreatorId() != null) {
            criteria.andCreatorIdEqualTo(query.getCreatorId());
        }
        
        if (query.getAddTimeStart() != null) {
            criteria.andAddTimeGreaterThanOrEqualTo(query.getAddTimeStart());
        }
        
        if (query.getAddTimeEnd() != null) {
            criteria.andAddTimeLessThanOrEqualTo(query.getAddTimeEnd());
        }
        
        return dataItemMapper.countByExample(example);
    }
} 