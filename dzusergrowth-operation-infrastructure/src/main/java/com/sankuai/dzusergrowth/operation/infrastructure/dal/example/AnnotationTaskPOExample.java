package com.sankuai.dzusergrowth.operation.infrastructure.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AnnotationTaskPOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public AnnotationTaskPOExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public AnnotationTaskPOExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public AnnotationTaskPOExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public AnnotationTaskPOExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAnnotationDatasetIdIsNull() {
            addCriterion("annotation_dataset_id is null");
            return (Criteria) this;
        }

        public Criteria andAnnotationDatasetIdIsNotNull() {
            addCriterion("annotation_dataset_id is not null");
            return (Criteria) this;
        }

        public Criteria andAnnotationDatasetIdEqualTo(Long value) {
            addCriterion("annotation_dataset_id =", value, "annotationDatasetId");
            return (Criteria) this;
        }

        public Criteria andAnnotationDatasetIdNotEqualTo(Long value) {
            addCriterion("annotation_dataset_id <>", value, "annotationDatasetId");
            return (Criteria) this;
        }

        public Criteria andAnnotationDatasetIdGreaterThan(Long value) {
            addCriterion("annotation_dataset_id >", value, "annotationDatasetId");
            return (Criteria) this;
        }

        public Criteria andAnnotationDatasetIdGreaterThanOrEqualTo(Long value) {
            addCriterion("annotation_dataset_id >=", value, "annotationDatasetId");
            return (Criteria) this;
        }

        public Criteria andAnnotationDatasetIdLessThan(Long value) {
            addCriterion("annotation_dataset_id <", value, "annotationDatasetId");
            return (Criteria) this;
        }

        public Criteria andAnnotationDatasetIdLessThanOrEqualTo(Long value) {
            addCriterion("annotation_dataset_id <=", value, "annotationDatasetId");
            return (Criteria) this;
        }

        public Criteria andAnnotationDatasetIdIn(List<Long> values) {
            addCriterion("annotation_dataset_id in", values, "annotationDatasetId");
            return (Criteria) this;
        }

        public Criteria andAnnotationDatasetIdNotIn(List<Long> values) {
            addCriterion("annotation_dataset_id not in", values, "annotationDatasetId");
            return (Criteria) this;
        }

        public Criteria andAnnotationDatasetIdBetween(Long value1, Long value2) {
            addCriterion("annotation_dataset_id between", value1, value2, "annotationDatasetId");
            return (Criteria) this;
        }

        public Criteria andAnnotationDatasetIdNotBetween(Long value1, Long value2) {
            addCriterion("annotation_dataset_id not between", value1, value2, "annotationDatasetId");
            return (Criteria) this;
        }

        public Criteria andTaskNameIsNull() {
            addCriterion("task_name is null");
            return (Criteria) this;
        }

        public Criteria andTaskNameIsNotNull() {
            addCriterion("task_name is not null");
            return (Criteria) this;
        }

        public Criteria andTaskNameEqualTo(String value) {
            addCriterion("task_name =", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotEqualTo(String value) {
            addCriterion("task_name <>", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameGreaterThan(String value) {
            addCriterion("task_name >", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameGreaterThanOrEqualTo(String value) {
            addCriterion("task_name >=", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameLessThan(String value) {
            addCriterion("task_name <", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameLessThanOrEqualTo(String value) {
            addCriterion("task_name <=", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameLike(String value) {
            addCriterion("task_name like", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotLike(String value) {
            addCriterion("task_name not like", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameIn(List<String> values) {
            addCriterion("task_name in", values, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotIn(List<String> values) {
            addCriterion("task_name not in", values, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameBetween(String value1, String value2) {
            addCriterion("task_name between", value1, value2, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotBetween(String value1, String value2) {
            addCriterion("task_name not between", value1, value2, "taskName");
            return (Criteria) this;
        }

        public Criteria andAnnotationTypeIsNull() {
            addCriterion("annotation_type is null");
            return (Criteria) this;
        }

        public Criteria andAnnotationTypeIsNotNull() {
            addCriterion("annotation_type is not null");
            return (Criteria) this;
        }

        public Criteria andAnnotationTypeEqualTo(Integer value) {
            addCriterion("annotation_type =", value, "annotationType");
            return (Criteria) this;
        }

        public Criteria andAnnotationTypeNotEqualTo(Integer value) {
            addCriterion("annotation_type <>", value, "annotationType");
            return (Criteria) this;
        }

        public Criteria andAnnotationTypeGreaterThan(Integer value) {
            addCriterion("annotation_type >", value, "annotationType");
            return (Criteria) this;
        }

        public Criteria andAnnotationTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("annotation_type >=", value, "annotationType");
            return (Criteria) this;
        }

        public Criteria andAnnotationTypeLessThan(Integer value) {
            addCriterion("annotation_type <", value, "annotationType");
            return (Criteria) this;
        }

        public Criteria andAnnotationTypeLessThanOrEqualTo(Integer value) {
            addCriterion("annotation_type <=", value, "annotationType");
            return (Criteria) this;
        }

        public Criteria andAnnotationTypeIn(List<Integer> values) {
            addCriterion("annotation_type in", values, "annotationType");
            return (Criteria) this;
        }

        public Criteria andAnnotationTypeNotIn(List<Integer> values) {
            addCriterion("annotation_type not in", values, "annotationType");
            return (Criteria) this;
        }

        public Criteria andAnnotationTypeBetween(Integer value1, Integer value2) {
            addCriterion("annotation_type between", value1, value2, "annotationType");
            return (Criteria) this;
        }

        public Criteria andAnnotationTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("annotation_type not between", value1, value2, "annotationType");
            return (Criteria) this;
        }

        public Criteria andAnnotationConfigIsNull() {
            addCriterion("annotation_config is null");
            return (Criteria) this;
        }

        public Criteria andAnnotationConfigIsNotNull() {
            addCriterion("annotation_config is not null");
            return (Criteria) this;
        }

        public Criteria andAnnotationConfigEqualTo(String value) {
            addCriterion("annotation_config =", value, "annotationConfig");
            return (Criteria) this;
        }

        public Criteria andAnnotationConfigNotEqualTo(String value) {
            addCriterion("annotation_config <>", value, "annotationConfig");
            return (Criteria) this;
        }

        public Criteria andAnnotationConfigGreaterThan(String value) {
            addCriterion("annotation_config >", value, "annotationConfig");
            return (Criteria) this;
        }

        public Criteria andAnnotationConfigGreaterThanOrEqualTo(String value) {
            addCriterion("annotation_config >=", value, "annotationConfig");
            return (Criteria) this;
        }

        public Criteria andAnnotationConfigLessThan(String value) {
            addCriterion("annotation_config <", value, "annotationConfig");
            return (Criteria) this;
        }

        public Criteria andAnnotationConfigLessThanOrEqualTo(String value) {
            addCriterion("annotation_config <=", value, "annotationConfig");
            return (Criteria) this;
        }

        public Criteria andAnnotationConfigLike(String value) {
            addCriterion("annotation_config like", value, "annotationConfig");
            return (Criteria) this;
        }

        public Criteria andAnnotationConfigNotLike(String value) {
            addCriterion("annotation_config not like", value, "annotationConfig");
            return (Criteria) this;
        }

        public Criteria andAnnotationConfigIn(List<String> values) {
            addCriterion("annotation_config in", values, "annotationConfig");
            return (Criteria) this;
        }

        public Criteria andAnnotationConfigNotIn(List<String> values) {
            addCriterion("annotation_config not in", values, "annotationConfig");
            return (Criteria) this;
        }

        public Criteria andAnnotationConfigBetween(String value1, String value2) {
            addCriterion("annotation_config between", value1, value2, "annotationConfig");
            return (Criteria) this;
        }

        public Criteria andAnnotationConfigNotBetween(String value1, String value2) {
            addCriterion("annotation_config not between", value1, value2, "annotationConfig");
            return (Criteria) this;
        }

        public Criteria andShowColumnIsNull() {
            addCriterion("show_column is null");
            return (Criteria) this;
        }

        public Criteria andShowColumnIsNotNull() {
            addCriterion("show_column is not null");
            return (Criteria) this;
        }

        public Criteria andShowColumnEqualTo(String value) {
            addCriterion("show_column =", value, "showColumn");
            return (Criteria) this;
        }

        public Criteria andShowColumnNotEqualTo(String value) {
            addCriterion("show_column <>", value, "showColumn");
            return (Criteria) this;
        }

        public Criteria andShowColumnGreaterThan(String value) {
            addCriterion("show_column >", value, "showColumn");
            return (Criteria) this;
        }

        public Criteria andShowColumnGreaterThanOrEqualTo(String value) {
            addCriterion("show_column >=", value, "showColumn");
            return (Criteria) this;
        }

        public Criteria andShowColumnLessThan(String value) {
            addCriterion("show_column <", value, "showColumn");
            return (Criteria) this;
        }

        public Criteria andShowColumnLessThanOrEqualTo(String value) {
            addCriterion("show_column <=", value, "showColumn");
            return (Criteria) this;
        }

        public Criteria andShowColumnLike(String value) {
            addCriterion("show_column like", value, "showColumn");
            return (Criteria) this;
        }

        public Criteria andShowColumnNotLike(String value) {
            addCriterion("show_column not like", value, "showColumn");
            return (Criteria) this;
        }

        public Criteria andShowColumnIn(List<String> values) {
            addCriterion("show_column in", values, "showColumn");
            return (Criteria) this;
        }

        public Criteria andShowColumnNotIn(List<String> values) {
            addCriterion("show_column not in", values, "showColumn");
            return (Criteria) this;
        }

        public Criteria andShowColumnBetween(String value1, String value2) {
            addCriterion("show_column between", value1, value2, "showColumn");
            return (Criteria) this;
        }

        public Criteria andShowColumnNotBetween(String value1, String value2) {
            addCriterion("show_column not between", value1, value2, "showColumn");
            return (Criteria) this;
        }

        public Criteria andAnnotatorIsNull() {
            addCriterion("annotator is null");
            return (Criteria) this;
        }

        public Criteria andAnnotatorIsNotNull() {
            addCriterion("annotator is not null");
            return (Criteria) this;
        }

        public Criteria andAnnotatorEqualTo(String value) {
            addCriterion("annotator =", value, "annotator");
            return (Criteria) this;
        }

        public Criteria andAnnotatorNotEqualTo(String value) {
            addCriterion("annotator <>", value, "annotator");
            return (Criteria) this;
        }

        public Criteria andAnnotatorGreaterThan(String value) {
            addCriterion("annotator >", value, "annotator");
            return (Criteria) this;
        }

        public Criteria andAnnotatorGreaterThanOrEqualTo(String value) {
            addCriterion("annotator >=", value, "annotator");
            return (Criteria) this;
        }

        public Criteria andAnnotatorLessThan(String value) {
            addCriterion("annotator <", value, "annotator");
            return (Criteria) this;
        }

        public Criteria andAnnotatorLessThanOrEqualTo(String value) {
            addCriterion("annotator <=", value, "annotator");
            return (Criteria) this;
        }

        public Criteria andAnnotatorLike(String value) {
            addCriterion("annotator like", value, "annotator");
            return (Criteria) this;
        }

        public Criteria andAnnotatorNotLike(String value) {
            addCriterion("annotator not like", value, "annotator");
            return (Criteria) this;
        }

        public Criteria andAnnotatorIn(List<String> values) {
            addCriterion("annotator in", values, "annotator");
            return (Criteria) this;
        }

        public Criteria andAnnotatorNotIn(List<String> values) {
            addCriterion("annotator not in", values, "annotator");
            return (Criteria) this;
        }

        public Criteria andAnnotatorBetween(String value1, String value2) {
            addCriterion("annotator between", value1, value2, "annotator");
            return (Criteria) this;
        }

        public Criteria andAnnotatorNotBetween(String value1, String value2) {
            addCriterion("annotator not between", value1, value2, "annotator");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIsNull() {
            addCriterion("creator_id is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIsNotNull() {
            addCriterion("creator_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorIdEqualTo(Long value) {
            addCriterion("creator_id =", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotEqualTo(Long value) {
            addCriterion("creator_id <>", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdGreaterThan(Long value) {
            addCriterion("creator_id >", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdGreaterThanOrEqualTo(Long value) {
            addCriterion("creator_id >=", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdLessThan(Long value) {
            addCriterion("creator_id <", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdLessThanOrEqualTo(Long value) {
            addCriterion("creator_id <=", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIn(List<Long> values) {
            addCriterion("creator_id in", values, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotIn(List<Long> values) {
            addCriterion("creator_id not in", values, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdBetween(Long value1, Long value2) {
            addCriterion("creator_id between", value1, value2, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotBetween(Long value1, Long value2) {
            addCriterion("creator_id not between", value1, value2, "creatorId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdIsNull() {
            addCriterion("updater_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdIsNotNull() {
            addCriterion("updater_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdEqualTo(Long value) {
            addCriterion("updater_id =", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdNotEqualTo(Long value) {
            addCriterion("updater_id <>", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdGreaterThan(Long value) {
            addCriterion("updater_id >", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdGreaterThanOrEqualTo(Long value) {
            addCriterion("updater_id >=", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdLessThan(Long value) {
            addCriterion("updater_id <", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdLessThanOrEqualTo(Long value) {
            addCriterion("updater_id <=", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdIn(List<Long> values) {
            addCriterion("updater_id in", values, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdNotIn(List<Long> values) {
            addCriterion("updater_id not in", values, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdBetween(Long value1, Long value2) {
            addCriterion("updater_id between", value1, value2, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdNotBetween(Long value1, Long value2) {
            addCriterion("updater_id not between", value1, value2, "updaterId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}