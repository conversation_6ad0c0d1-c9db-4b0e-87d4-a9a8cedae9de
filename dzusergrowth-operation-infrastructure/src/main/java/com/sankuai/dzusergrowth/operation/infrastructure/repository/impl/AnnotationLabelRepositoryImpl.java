package com.sankuai.dzusergrowth.operation.infrastructure.repository.impl;

import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationLabelDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.AnnotationLabelQuery;
import com.sankuai.dzusergrowth.operation.domain.repository.AnnotationLabelRepository;
import com.sankuai.dzusergrowth.operation.infrastructure.converter.AnnotationLabelConverter;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.example.AnnotationLabelPOExample;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.mapper.AnnotationLabelPOMapper;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.po.AnnotationLabelPO;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标注标签仓储实现类
 */
@Repository
public class AnnotationLabelRepositoryImpl implements AnnotationLabelRepository {

    @Resource
    private AnnotationLabelPOMapper annotationLabelMapper;

    /**
     * 创建新的标注标签
     *
     * @param annotationLabelDO 标注标签信息DO
     */
    @Override
    public void createAnnotationLabel(AnnotationLabelDO annotationLabelDO) {
        if (annotationLabelDO == null) {
            throw new IllegalArgumentException("标注标签对象不能为空");
        }
        
        // 根据AnnotationLabelDO构建AnnotationLabel PO对象
        AnnotationLabelPO annotationLabelPO = AnnotationLabelConverter.convertDOToPO(annotationLabelDO);
        // 保存标注标签到数据库
        annotationLabelMapper.insertSelective(annotationLabelPO);
        
        // 设置生成的ID
        annotationLabelDO.setId(annotationLabelPO.getId());
    }

    /**
     * 根据ID获取标注标签
     *
     * @param labelId 标签ID
     * @return 标注标签信息，如不存在则返回null
     */
    @Override
    public AnnotationLabelDO getAnnotationLabelById(Long labelId) {
        if (labelId == null) {
            return null;
        }
        
        AnnotationLabelPOExample example = new AnnotationLabelPOExample();
        example.createCriteria().andIdEqualTo(labelId);

        // 使用ZebraForceMasterHelper强制查询主库
        try {
            ZebraForceMasterHelper.forceMasterInLocalContext();
            List<AnnotationLabelPO> annotationLabelPOS = annotationLabelMapper.selectByExample(example);
            if (annotationLabelPOS == null || annotationLabelPOS.isEmpty()) {
                return null;
            }

            return AnnotationLabelConverter.convertPOToDO(annotationLabelPOS.get(0));
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
    }
    
    /**
     * 更新标注标签
     *
     * @param annotationLabelDO 标注标签DO
     */
    @Override
    public void updateAnnotationLabel(AnnotationLabelDO annotationLabelDO) {
        if (annotationLabelDO == null) {
            throw new IllegalArgumentException("标注标签对象不能为空");
        }
        
        if (annotationLabelDO.getId() == null) {
            throw new IllegalArgumentException("标注标签ID不能为空");
        }
        
        // 构建更新对象
        AnnotationLabelPO annotationLabelPO = AnnotationLabelConverter.convertDOToPO(annotationLabelDO);
        
        // 构建条件
        AnnotationLabelPOExample example = new AnnotationLabelPOExample();
        example.createCriteria().andIdEqualTo(annotationLabelDO.getId());
        
        // 执行更新
        int result = annotationLabelMapper.updateByExampleSelective(annotationLabelPO, example);
        
        if (result != 1) {
            throw new IllegalStateException("更新标注标签失败，标签ID: " + annotationLabelDO.getId() + "，可能标签不存在");
        }
    }
    
    /**
     * 删除标注标签
     *
     * @param labelId 标签ID
     */
    @Override
    public void deleteAnnotationLabel(Long labelId) {
        if (labelId == null) {
            throw new IllegalArgumentException("标注标签ID不能为空");
        }
        
        AnnotationLabelPOExample example = new AnnotationLabelPOExample();
        example.createCriteria().andIdEqualTo(labelId);
        
        int result = annotationLabelMapper.deleteByExample(example);
        
        if (result != 1) {
            throw new IllegalStateException("删除标注标签失败，标签ID: " + labelId + "，可能标签不存在");
        }
    }
    
    /**
     * 批量查询标注标签
     *
     * @param query 标注标签查询条件
     * @return 标注标签DO列表
     */
    @Override
    public List<AnnotationLabelDO> queryAnnotationLabel(AnnotationLabelQuery query) {
        if (query == null) {
            throw new IllegalArgumentException("查询条件不能为空");
        }
        
        AnnotationLabelPOExample example = new AnnotationLabelPOExample();
        AnnotationLabelPOExample.Criteria criteria = example.createCriteria();
        
        // 构建查询条件
        if (!CollectionUtils.isEmpty(query.getLabelIds())) {
            criteria.andIdIn(query.getLabelIds());
        }
        
        if (query.getEffectiveLabelType() != null) {
            criteria.andLabelTypeEqualTo(query.getEffectiveLabelType());
        }
        
        if (query.getCreatorId() != null) {
            criteria.andCreatorIdEqualTo(query.getCreatorId());
        }
        
        if (query.getAddTimeStart() != null) {
            criteria.andAddTimeGreaterThanOrEqualTo(query.getAddTimeStart());
        }
        
        if (query.getAddTimeEnd() != null) {
            criteria.andAddTimeLessThanOrEqualTo(query.getAddTimeEnd());
        }
        
        // 设置排序
        if (StringUtils.hasText(query.getOrderBy())) {
            String orderClause = query.getOrderBy();
            if (StringUtils.hasText(query.getOrderDirection())) {
                orderClause += " " + query.getOrderDirection();
            }
            example.setOrderByClause(orderClause);
        } else {
            example.setOrderByClause("add_time DESC");
        }
        
        // 处理分页
        if (query.getPageSize() != null && query.getPageSize() > 0) {
            int pageNum = query.getPageNum() != null && query.getPageNum() > 0 ? query.getPageNum() : 1;
            int offset = (pageNum - 1) * query.getPageSize();
            example.setOffset(offset);
            example.setRows(query.getPageSize());
        }
        
        List<AnnotationLabelPO> annotationLabelPOS = annotationLabelMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(annotationLabelPOS)) {
            return Collections.emptyList();
        }
        
        return annotationLabelPOS.stream()
                .map(AnnotationLabelConverter::convertPOToDO)
                .collect(Collectors.toList());
    }
} 