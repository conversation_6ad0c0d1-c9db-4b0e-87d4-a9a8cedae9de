package com.sankuai.dzusergrowth.operation.infrastructure.repository.impl;

import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.DatasetQuery;
import com.sankuai.dzusergrowth.operation.domain.repository.DatasetRepository;
import com.sankuai.dzusergrowth.operation.infrastructure.converter.DatasetConverter;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DatasetPOExample;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.mapper.DatasetPOMapper;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DatasetPO;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据集仓储实现类
 */
@Repository
public class DatasetRepositoryImpl implements DatasetRepository {

    @Resource
    private DatasetPOMapper datasetMapper;

    /**
     * 创建新的数据集
     *
     * @param datasetDO 数据集信息DO
     */
    @Override
    public void createDataset(DatasetDO datasetDO) {
        if (datasetDO == null) {
            throw new IllegalArgumentException("数据集对象不能为空");
        }
        
        // 根据DatasetDO构建Dataset PO对象
        DatasetPO datasetPO = DatasetConverter.convertDOToPO(datasetDO);
        // 保存数据集到数据库
        datasetMapper.insertSelective(datasetPO);
        
        // 设置生成的ID
        datasetDO.setId(datasetPO.getId());
    }

    /**
     * 根据ID获取数据集
     *
     * @param datasetId 数据集ID
     * @return 数据集信息，如不存在则返回null
     */
    @Override
    public DatasetDO getDatasetById(Long datasetId) {
        if (datasetId == null) {
            return null;
        }
        
        DatasetPOExample example = new DatasetPOExample();
        example.createCriteria().andIdEqualTo(datasetId);

        // 使用ZebraForceMasterHelper强制查询主库
        try {
            ZebraForceMasterHelper.forceMasterInLocalContext();
            List<DatasetPO> datasetPOS = datasetMapper.selectByExample(example);
            if (datasetPOS == null || datasetPOS.isEmpty()) {
                return null;
            }

            return DatasetConverter.convertPOToDO(datasetPOS.get(0));
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
    }
    
    /**
     * 更新数据集
     *
     * @param datasetDO 数据集DO
     */
    @Override
    public void updateDataset(DatasetDO datasetDO) {
        if (datasetDO == null) {
            throw new IllegalArgumentException("数据集对象不能为空");
        }
        
        if (datasetDO.getId() == null) {
            throw new IllegalArgumentException("数据集ID不能为空");
        }
        
        // 构建更新对象
        DatasetPO datasetPO = DatasetConverter.convertDOToPO(datasetDO);
        
        // 构建条件
        DatasetPOExample example = new DatasetPOExample();
        example.createCriteria().andIdEqualTo(datasetDO.getId());
        
        // 执行更新
        int result = datasetMapper.updateByExampleSelective(datasetPO, example);
        
        if (result != 1) {
            throw new IllegalStateException("更新数据集失败，数据集ID: " + datasetDO.getId() + "，可能数据集不存在");
        }
    }
    
    /**
     * 删除数据集
     *
     * @param datasetId 数据集ID
     */
    @Override
    public void deleteDataset(Long datasetId) {
        if (datasetId == null) {
            throw new IllegalArgumentException("数据集ID不能为空");
        }
        
        DatasetPOExample example = new DatasetPOExample();
        example.createCriteria().andIdEqualTo(datasetId);
        
        int result = datasetMapper.deleteByExample(example);
        
        if (result != 1) {
            throw new IllegalStateException("删除数据集失败，数据集ID: " + datasetId + "，可能数据集不存在");
        }
    }
    
    /**
     * 批量查询数据集
     *
     * @param query 数据集查询条件
     * @return 数据集DO列表
     */
    @Override
    public List<DatasetDO> queryDataset(DatasetQuery query) {
        if (query == null) {
            throw new IllegalArgumentException("查询条件不能为空");
        }
        
        DatasetPOExample example = new DatasetPOExample();
        DatasetPOExample.Criteria criteria = example.createCriteria();
        
        // 构建查询条件
        if (!CollectionUtils.isEmpty(query.getDatasetIds())) {
            criteria.andIdIn(query.getDatasetIds());
        }
        
        if (query.getDatasetType() != null) {
            criteria.andDatasetTypeEqualTo(query.getDatasetType());
        }
        
        if (query.getDataGranularity() != null) {
            criteria.andDataGranularityEqualTo(query.getDataGranularity());
        }
        
        if (StringUtils.hasText(query.getName())) {
            criteria.andNameLike("%" + query.getName() + "%");
        }
        
        if (query.getGenerateType() != null) {
            criteria.andGenerateTypeEqualTo(query.getGenerateType());
        }
        
        if (query.getCreatorId() != null) {
            criteria.andCreatorIdEqualTo(query.getCreatorId());
        }
        
        if (query.getAddTimeStart() != null) {
            criteria.andAddTimeGreaterThanOrEqualTo(query.getAddTimeStart());
        }
        
        if (query.getAddTimeEnd() != null) {
            criteria.andAddTimeLessThanOrEqualTo(query.getAddTimeEnd());
        }
        
        // 设置排序
        if (StringUtils.hasText(query.getOrderBy())) {
            String orderClause = query.getOrderBy();
            if (StringUtils.hasText(query.getOrderDirection())) {
                orderClause += " " + query.getOrderDirection();
            }
            example.setOrderByClause(orderClause);
        } else {
            example.setOrderByClause("add_time DESC");
        }
        
        // 处理分页
        if (query.getPageSize() != null && query.getPageSize() > 0) {
            int pageNum = query.getPageNum() != null && query.getPageNum() > 0 ? query.getPageNum() : 1;
            int offset = (pageNum - 1) * query.getPageSize();
            example.setOffset(offset);
            example.setRows(query.getPageSize());
        }
        
        List<DatasetPO> datasetPOS = datasetMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(datasetPOS)) {
            return Collections.emptyList();
        }
        
        return datasetPOS.stream()
                .map(DatasetConverter::convertPOToDO)
                .collect(Collectors.toList());
    }
    
    /**
     * 统计符合条件的数据集数量（用于分页查询）
     *
     * @param query 数据集查询条件
     * @return 数据集数量
     */
    @Override
    public long countDataset(DatasetQuery query) {
        if (query == null) {
            throw new IllegalArgumentException("查询条件不能为空");
        }
        
        DatasetPOExample example = new DatasetPOExample();
        DatasetPOExample.Criteria criteria = example.createCriteria();
        
        // 构建查询条件（与queryDataset方法保持一致，但不需要分页和排序）
        if (!CollectionUtils.isEmpty(query.getDatasetIds())) {
            criteria.andIdIn(query.getDatasetIds());
        }
        
        if (query.getDatasetType() != null) {
            criteria.andDatasetTypeEqualTo(query.getDatasetType());
        }
        
        if (query.getDataGranularity() != null) {
            criteria.andDataGranularityEqualTo(query.getDataGranularity());
        }
        
        if (StringUtils.hasText(query.getName())) {
            criteria.andNameLike("%" + query.getName() + "%");
        }
        
        if (query.getGenerateType() != null) {
            criteria.andGenerateTypeEqualTo(query.getGenerateType());
        }
        
        if (query.getCreatorId() != null) {
            criteria.andCreatorIdEqualTo(query.getCreatorId());
        }
        
        if (query.getAddTimeStart() != null) {
            criteria.andAddTimeGreaterThanOrEqualTo(query.getAddTimeStart());
        }
        
        if (query.getAddTimeEnd() != null) {
            criteria.andAddTimeLessThanOrEqualTo(query.getAddTimeEnd());
        }
        
        return datasetMapper.countByExample(example);
    }
} 