package com.sankuai.dzusergrowth.operation.infrastructure.dal.po;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: ai_admin_dataset
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DatasetPO {
    /**
     *   字段: id
     *   说明: 数据集id
     */
    private Long id;

    /**
     *   字段: dataset_type
     *   说明: 数据集类型: 1-原始数据集; 2-评测结果集
     */
    private Integer datasetType;

    /**
     *   字段: data_granularity
     *   说明: 数据粒度: 1-活动; 2-任务; 3-query; 4-log; 5-资产
     */
    private Integer dataGranularity;

    /**
     *   字段: name
     *   说明: 数据集名称
     */
    private String name;

    /**
     *   字段: generate_type
     *   说明: 数据集生成方式: 1-条件来源; 2-手动上传; 3-明确来源
     */
    private Integer generateType;

    /**
     *   字段: generate_config
     *   说明: 生成配置
     */
    private String generateConfig;

    /**
     *   字段: task_status
     *   说明: 任务状态；1：执行中；2：执行成功；3：执行失败
     */
    private Integer taskStatus;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: creator_id
     *   说明: 创建人ID
     */
    private Long creatorId;

    /**
     *   字段: updater_id
     *   说明: 更新人ID
     */
    private Long updaterId;

    /**
     *   字段: description
     *   说明: 数据集描述
     */
    private String description;
}