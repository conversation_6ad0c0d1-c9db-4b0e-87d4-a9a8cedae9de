package com.sankuai.dzusergrowth.operation.infrastructure.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.dzusergrowth.operation.api.enums.DataTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.ColumnTypeEnum;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetColumnInfoDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetColumnConfig;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DatasetColumnInfoPO;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据集列信息转换器
 */
@Slf4j
public class DatasetColumnInfoConverter {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * DO转PO
     */
    public static DatasetColumnInfoPO convertDOToPO(DatasetColumnInfoDO columnInfoDO) {
        if (columnInfoDO == null) {
            return null;
        }
        
        DatasetColumnInfoPO columnInfo = new DatasetColumnInfoPO();
        columnInfo.setId(columnInfoDO.getId());
        columnInfo.setDatasetId(columnInfoDO.getDatasetId());
        columnInfo.setName(columnInfoDO.getName());
        columnInfo.setDisplayName(columnInfoDO.getDisplayName());
        
        // 枚举转换为Integer
        if (columnInfoDO.getColumnType() != null) {
            columnInfo.setColumnType(columnInfoDO.getColumnType().getCode());
        }
        if (columnInfoDO.getDataType() != null) {
            columnInfo.setDataType(columnInfoDO.getDataType().getCode());
        }
        
        columnInfo.setCreatorId(columnInfoDO.getCreatorId());
        columnInfo.setUpdaterId(columnInfoDO.getUpdaterId());
        columnInfo.setAddTime(columnInfoDO.getAddTime());
        columnInfo.setUpdateTime(columnInfoDO.getUpdateTime());
        
        // 将配置对象转换为JSON字符串
        if (columnInfoDO.getColumnConfig() != null) {
            try {
                String configJson = OBJECT_MAPPER.writeValueAsString(columnInfoDO.getColumnConfig());
                columnInfo.setColumnConfig(configJson);
            } catch (JsonProcessingException e) {
                log.error("Failed to convert columnConfig to JSON for columnInfo: {}", columnInfoDO.getId(), e);
                columnInfo.setColumnConfig(null);
            }
        }
        
        return columnInfo;
    }

    /**
     * PO转DO
     */
    public static DatasetColumnInfoDO convertPOToDO(DatasetColumnInfoPO columnInfo) {
        if (columnInfo == null) {
            return null;
        }
        
        DatasetColumnInfoDO.DatasetColumnInfoDOBuilder builder = DatasetColumnInfoDO.builder()
                .id(columnInfo.getId())
                .datasetId(columnInfo.getDatasetId())
                .name(columnInfo.getName())
                .displayName(columnInfo.getDisplayName())
                .creatorId(columnInfo.getCreatorId())
                .updaterId(columnInfo.getUpdaterId())
                .addTime(columnInfo.getAddTime())
                .updateTime(columnInfo.getUpdateTime());
        
        // Integer转换为枚举
        if (columnInfo.getColumnType() != null) {
            ColumnTypeEnum columnType = ColumnTypeEnum.fromCode(columnInfo.getColumnType());
            builder.columnType(columnType);
        }
        if (columnInfo.getDataType() != null) {
            DataTypeEnum dataType = DataTypeEnum.fromCode(columnInfo.getDataType());
            builder.dataType(dataType);
        }
        
        // 将JSON字符串转换为配置对象
        if (columnInfo.getColumnConfig() != null && !columnInfo.getColumnConfig().trim().isEmpty()) {
            try {
                DatasetColumnConfig config = OBJECT_MAPPER.readValue(
                    columnInfo.getColumnConfig(), 
                    DatasetColumnConfig.class
                );
                builder.columnConfig(config);
            } catch (JsonProcessingException e) {
                log.error("Failed to parse columnConfig JSON for columnInfo: {}", columnInfo.getId(), e);
                // 解析失败时，配置对象为null
                builder.columnConfig(null);
            }
        }
        
        return builder.build();
    }
} 