package com.sankuai.dzusergrowth.operation.infrastructure.converter;

import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationLabelDO;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.po.AnnotationLabelPO;
import com.sankuai.dzusergrowth.operation.infrastructure.utils.JsonUtils;

import java.util.List;

/**
 * 标注标签转换器
 */
public class AnnotationLabelConverter {

    /**
     * DO转PO
     */
    public static AnnotationLabelPO convertDOToPO(AnnotationLabelDO annotationLabelDO) {
        if (annotationLabelDO == null) {
            return null;
        }
        
        AnnotationLabelPO annotationLabelPO = new AnnotationLabelPO();
        annotationLabelPO.setId(annotationLabelDO.getId());
        
        // List<String>转换为JSON字符串
        annotationLabelPO.setLabelConfig(
                convertLabelConfigToJson(annotationLabelDO.getLabelConfig()));
        
        annotationLabelPO.setCreatorId(annotationLabelDO.getCreatorId());
        annotationLabelPO.setUpdaterId(annotationLabelDO.getUpdaterId());
        annotationLabelPO.setAddTime(annotationLabelDO.getAddTime());
        annotationLabelPO.setUpdateTime(annotationLabelDO.getUpdateTime());
        
        return annotationLabelPO;
    }

    /**
     * PO转DO
     */
    public static AnnotationLabelDO convertPOToDO(AnnotationLabelPO annotationLabelPO) {
        if (annotationLabelPO == null) {
            return null;
        }
        
        return AnnotationLabelDO.builder()
                .id(annotationLabelPO.getId())
                
                // JSON字符串转换为List<String>
                .labelConfig(convertJsonToLabelConfig(annotationLabelPO.getLabelConfig()))
                
                .creatorId(annotationLabelPO.getCreatorId())
                .updaterId(annotationLabelPO.getUpdaterId())
                .addTime(annotationLabelPO.getAddTime())
                .updateTime(annotationLabelPO.getUpdateTime())
                .build();
    }
    
    /**
     * 标签配置列表转换为JSON字符串
     */
    private static String convertLabelConfigToJson(List<String> labelConfigList) {
        return JsonUtils.listToJsonString(labelConfigList);
    }
    
    /**
     * JSON字符串转换为标签配置列表
     */
    private static List<String> convertJsonToLabelConfig(String jsonStr) {
        return JsonUtils.jsonStringToStringList(jsonStr);
    }
} 