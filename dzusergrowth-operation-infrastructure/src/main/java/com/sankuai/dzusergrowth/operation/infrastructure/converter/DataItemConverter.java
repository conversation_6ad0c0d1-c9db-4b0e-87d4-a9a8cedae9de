package com.sankuai.dzusergrowth.operation.infrastructure.converter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DataItemDO;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DataItemPO;
import com.sankuai.dzusergrowth.operation.infrastructure.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 数据条目转换器
 */
@Slf4j
public class DataItemConverter {

    private static final TypeReference<Map<Long, String>> MAP_TYPE_REFERENCE = new TypeReference<Map<Long, String>>() {};

    /**
     * DO转PO
     */
    public static DataItemPO convertDOToPO(DataItemDO dataItemDO) {
        if (dataItemDO == null) {
            return null;
        }
        
        DataItemPO dataItemPO = new DataItemPO();
        dataItemPO.setId(dataItemDO.getId());
        dataItemPO.setDatasetId(dataItemDO.getDatasetId());
        dataItemPO.setDataUniqueKey(dataItemDO.getDataUniqueKey());
        dataItemPO.setIsDeleted(dataItemDO.getIsDeleted());
        dataItemPO.setCreatorId(dataItemDO.getCreatorId());
        dataItemPO.setUpdaterId(dataItemDO.getUpdaterId());
        dataItemPO.setAddTime(dataItemDO.getAddTime());
        dataItemPO.setUpdateTime(dataItemDO.getUpdateTime());
        
        // 使用JsonUtils将Map对象转换为JSON字符串
        if (dataItemDO.getData() != null) {
            String dataJson = JsonUtils.toJsonString(dataItemDO.getData());
            if (dataJson == null) {
                log.error("Failed to convert data map to JSON for dataItem: {}", dataItemDO.getId());
            }
            dataItemPO.setData(dataJson);
        }
        
        return dataItemPO;
    }

    /**
     * PO转DO
     */
    public static DataItemDO convertPOToDO(DataItemPO dataItemPO) {
        if (dataItemPO == null) {
            return null;
        }
        
        DataItemDO.DataItemDOBuilder builder = DataItemDO.builder()
                .id(dataItemPO.getId())
                .datasetId(dataItemPO.getDatasetId())
                .dataUniqueKey(dataItemPO.getDataUniqueKey())
                .isDeleted(dataItemPO.getIsDeleted())
                .creatorId(dataItemPO.getCreatorId())
                .updaterId(dataItemPO.getUpdaterId())
                .addTime(dataItemPO.getAddTime())
                .updateTime(dataItemPO.getUpdateTime());
        
        // 使用JsonUtils将JSON字符串转换为Map对象
        if (dataItemPO.getData() != null && !dataItemPO.getData().trim().isEmpty()) {
            Map<Long, String> dataMap = JsonUtils.fromJsonString(
                dataItemPO.getData(), 
                MAP_TYPE_REFERENCE
            );
            if (dataMap == null) {
                log.error("Failed to parse data JSON to map for dataItem: {}", dataItemPO.getId());
            }
            builder.data(dataMap);
        }
        
        return builder.build();
    }
} 