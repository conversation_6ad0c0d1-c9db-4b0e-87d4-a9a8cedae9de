package com.sankuai.dzusergrowth.operation.infrastructure.converter;

import com.sankuai.dzusergrowth.operation.api.enums.EvaluationTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.TaskStatusEnum;
import com.sankuai.dzusergrowth.operation.domain.model.evaluation.EvaluationIndicatorConfig;
import com.sankuai.dzusergrowth.operation.domain.model.evaluation.EvaluationTaskDO;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.po.EvaluationTaskPO;
import com.sankuai.dzusergrowth.operation.infrastructure.utils.JsonUtils;

import java.util.List;
import java.util.Map;

/**
 * 评测任务转换器
 */
public class EvaluationTaskConverter {

    /**
     * DO转PO
     */
    public static EvaluationTaskPO convertDOToPO(EvaluationTaskDO evaluationTaskDO) {
        if (evaluationTaskDO == null) {
            return null;
        }
        
        EvaluationTaskPO evaluationTaskPO = new EvaluationTaskPO();
        evaluationTaskPO.setId(evaluationTaskDO.getId());
        evaluationTaskPO.setSourceDatasetId(evaluationTaskDO.getSourceDatasetId());
        evaluationTaskPO.setTaskName(evaluationTaskDO.getTaskName());
        
        // EvaluationTypeEnum转换为Integer
        if (evaluationTaskDO.getEvaluationType() != null) {
            evaluationTaskPO.setEvaluationType(evaluationTaskDO.getEvaluationType().getCode());
        }
        
        evaluationTaskPO.setEvaluationDatasetId(evaluationTaskDO.getEvaluationDatasetId());
        
        // List<EvaluationMetricConfig>转换为JSON字符串
        if (evaluationTaskDO.getEvaluationConfig() != null) {
            evaluationTaskPO.setEvaluationConfig(JsonUtils.toJson(evaluationTaskDO.getEvaluationConfig()));
        }
        
        // TaskStatusEnum转换为Integer
        if (evaluationTaskDO.getStatus() != null) {
            evaluationTaskPO.setStatus(evaluationTaskDO.getStatus().getCode());
        }
        
        // Map<String, Object>转换为JSON字符串
        if (evaluationTaskDO.getEvaluationData() != null) {
            evaluationTaskPO.setEvaluationData(JsonUtils.toJson(evaluationTaskDO.getEvaluationData()));
        }
        
        evaluationTaskPO.setCreatorId(evaluationTaskDO.getCreatorId());
        evaluationTaskPO.setUpdaterId(evaluationTaskDO.getUpdaterId());
        evaluationTaskPO.setDescription(evaluationTaskDO.getDescription());
        evaluationTaskPO.setAddTime(evaluationTaskDO.getAddTime());
        evaluationTaskPO.setUpdateTime(evaluationTaskDO.getUpdateTime());
        
        return evaluationTaskPO;
    }

    /**
     * PO转DO
     */
    public static EvaluationTaskDO convertPOToDO(EvaluationTaskPO evaluationTaskPO) {
        if (evaluationTaskPO == null) {
            return null;
        }
        
        EvaluationTaskDO.EvaluationTaskDOBuilder builder = EvaluationTaskDO.builder()
                .id(evaluationTaskPO.getId())
                .sourceDatasetId(evaluationTaskPO.getSourceDatasetId())
                .taskName(evaluationTaskPO.getTaskName())
                .evaluationDatasetId(evaluationTaskPO.getEvaluationDatasetId())
                .creatorId(evaluationTaskPO.getCreatorId())
                .updaterId(evaluationTaskPO.getUpdaterId())
                .description(evaluationTaskPO.getDescription())
                .addTime(evaluationTaskPO.getAddTime())
                .updateTime(evaluationTaskPO.getUpdateTime());
        
        // Integer转换为EvaluationTypeEnum
        if (evaluationTaskPO.getEvaluationType() != null) {
            EvaluationTypeEnum evaluationType = EvaluationTypeEnum.fromCode(evaluationTaskPO.getEvaluationType());
            builder.evaluationType(evaluationType);
        }
        
        // JSON字符串转换为List<EvaluationMetricConfig>
        if (evaluationTaskPO.getEvaluationConfig() != null) {
            List<EvaluationIndicatorConfig> evaluationConfig = JsonUtils.fromJsonList(
                    evaluationTaskPO.getEvaluationConfig(), EvaluationIndicatorConfig.class);
            builder.evaluationConfig(evaluationConfig);
        }
        
        // Integer转换为TaskStatusEnum
        if (evaluationTaskPO.getStatus() != null) {
            TaskStatusEnum taskStatus = TaskStatusEnum.fromCode(evaluationTaskPO.getStatus());
            builder.status(taskStatus);
        }
        
        // JSON字符串转换为Map<String, Object>
        if (evaluationTaskPO.getEvaluationData() != null) {
            Map<String, Object> evaluationData = JsonUtils.fromJsonMap(evaluationTaskPO.getEvaluationData());
            builder.evaluationData(evaluationData);
        }
        
        return builder.build();
    }
} 