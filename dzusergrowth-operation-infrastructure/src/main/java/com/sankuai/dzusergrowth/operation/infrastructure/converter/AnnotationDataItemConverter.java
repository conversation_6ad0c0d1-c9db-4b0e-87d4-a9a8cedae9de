package com.sankuai.dzusergrowth.operation.infrastructure.converter;

import com.sankuai.dzusergrowth.operation.api.enums.AnnotationDataItemStatusEnum;
import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationDataItemDO;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.po.AnnotationDataItemPO;

/**
 * 标注数据条目转换器
 */
public class AnnotationDataItemConverter {

    /**
     * DO转PO
     */
    public static AnnotationDataItemPO convertDOToPO(AnnotationDataItemDO annotationDataItemDO) {
        if (annotationDataItemDO == null) {
            return null;
        }
        
        AnnotationDataItemPO annotationDataItemPO = new AnnotationDataItemPO();
        annotationDataItemPO.setId(annotationDataItemDO.getId());
        annotationDataItemPO.setDatasetId(annotationDataItemDO.getDatasetId());
        annotationDataItemPO.setDataUniqueKey(annotationDataItemDO.getDataUniqueKey());
        annotationDataItemPO.setTaskId(annotationDataItemDO.getTaskId());
        annotationDataItemPO.setStatus(annotationDataItemDO.getStatus() != null ? annotationDataItemDO.getStatus().getCode() : null);
        annotationDataItemPO.setCreatorId(annotationDataItemDO.getCreatorId());
        annotationDataItemPO.setUpdaterId(annotationDataItemDO.getUpdaterId());
        annotationDataItemPO.setAddTime(annotationDataItemDO.getAddTime());
        annotationDataItemPO.setUpdateTime(annotationDataItemDO.getUpdateTime());
        
        return annotationDataItemPO;
    }

    /**
     * PO转DO
     */
    public static AnnotationDataItemDO convertPOToDO(AnnotationDataItemPO annotationDataItemPO) {
        if (annotationDataItemPO == null) {
            return null;
        }
        
        return AnnotationDataItemDO.builder()
                .id(annotationDataItemPO.getId())
                .datasetId(annotationDataItemPO.getDatasetId())
                .dataUniqueKey(annotationDataItemPO.getDataUniqueKey())
                .taskId(annotationDataItemPO.getTaskId())
                .status(AnnotationDataItemStatusEnum.fromCode(annotationDataItemPO.getStatus()))
                .creatorId(annotationDataItemPO.getCreatorId())
                .updaterId(annotationDataItemPO.getUpdaterId())
                .addTime(annotationDataItemPO.getAddTime())
                .updateTime(annotationDataItemPO.getUpdateTime())
                .build();
    }
} 