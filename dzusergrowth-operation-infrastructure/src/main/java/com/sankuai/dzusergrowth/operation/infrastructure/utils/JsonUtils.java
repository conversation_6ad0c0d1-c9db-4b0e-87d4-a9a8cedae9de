package com.sankuai.dzusergrowth.operation.infrastructure.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * JSON工具类
 * 提供通用的JSON编解码功能
 *
 * <AUTHOR>
 */
@Slf4j
public class JsonUtils {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 私有构造函数，防止实例化
     */
    private JsonUtils() {
        throw new UnsupportedOperationException("JsonUtils is a utility class and cannot be instantiated");
    }

    /**
     * 将对象转换为JSON字符串
     *
     * @param object 待转换的对象
     * @return JSON字符串，转换失败时返回null
     */
    public static String toJsonString(Object object) {
        if (object == null) {
            return null;
        }

        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (Exception e) {
            log.error("对象转换为JSON字符串失败: {}", object, e);
            return null;
        }
    }

    /**
     * 将对象转换为JSON字符串（简化方法名）
     *
     * @param object 待转换的对象
     * @return JSON字符串，转换失败时返回null
     */
    public static String toJson(Object object) {
        return toJsonString(object);
    }

    /**
     * 将JSON字符串转换为指定类型的对象
     *
     * @param jsonString JSON字符串
     * @param clazz      目标类型
     * @param <T>        泛型类型
     * @return 转换后的对象，转换失败时返回null
     */
    public static <T> T fromJsonString(String jsonString, Class<T> clazz) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }

        try {
            return OBJECT_MAPPER.readValue(jsonString, clazz);
        } catch (Exception e) {
            log.error("JSON字符串转换为对象失败: {}, 目标类型: {}", jsonString, clazz.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为指定类型的对象（使用TypeReference）
     *
     * @param jsonString    JSON字符串
     * @param typeReference 类型引用
     * @param <T>           泛型类型
     * @return 转换后的对象，转换失败时返回null
     */
    public static <T> T fromJsonString(String jsonString, TypeReference<T> typeReference) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }

        try {
            return OBJECT_MAPPER.readValue(jsonString, typeReference);
        } catch (Exception e) {
            log.error("JSON字符串转换为对象失败: {}, 目标类型: {}", jsonString, typeReference.getType(), e);
            return null;
        }
    }

    /**
     * 将List对象转换为JSON字符串
     *
     * @param list 待转换的List对象
     * @param <T>  List元素类型
     * @return JSON字符串，转换失败时返回null
     */
    public static <T> String listToJsonString(List<T> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }

        return toJsonString(list);
    }

    /**
     * 将JSON字符串转换为List对象
     *
     * @param jsonString JSON字符串
     * @param elementType List元素类型
     * @param <T>        List元素泛型类型
     * @return 转换后的List对象，转换失败时返回空List
     */
    public static <T> List<T> jsonStringToList(String jsonString, Class<T> elementType) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return Collections.emptyList();
        }

        try {
            TypeReference<List<T>> typeReference = new TypeReference<List<T>>() {};
            List<T> result = OBJECT_MAPPER.readValue(jsonString, 
                OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, elementType));
            return result != null ? result : Collections.emptyList();
        } catch (Exception e) {
            log.error("JSON字符串转换为List失败: {}, 元素类型: {}", jsonString, elementType.getSimpleName(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 将JSON字符串转换为List<String>
     *
     * @param jsonString JSON字符串
     * @return 转换后的List<String>，转换失败时返回空List
     */
    public static List<String> jsonStringToStringList(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return Collections.emptyList();
        }

        try {
            TypeReference<List<String>> typeReference = new TypeReference<List<String>>() {};
            List<String> result = OBJECT_MAPPER.readValue(jsonString, typeReference);
            return result != null ? result : Collections.emptyList();
        } catch (Exception e) {
            log.error("JSON字符串转换为String List失败: {}", jsonString, e);
            return Collections.emptyList();
        }
    }

    /**
     * 将JSON字符串转换为指定类型的List对象（简化方法名）
     *
     * @param jsonString JSON字符串
     * @param elementType List元素类型
     * @param <T>        List元素泛型类型
     * @return 转换后的List对象，转换失败时返回空List
     */
    public static <T> List<T> fromJsonList(String jsonString, Class<T> elementType) {
        return jsonStringToList(jsonString, elementType);
    }

    /**
     * 将JSON字符串转换为Map<String, Object>
     *
     * @param jsonString JSON字符串
     * @return 转换后的Map<String, Object>，转换失败时返回空Map
     */
    public static Map<String, Object> fromJsonMap(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            TypeReference<Map<String, Object>> typeReference = new TypeReference<Map<String, Object>>() {};
            Map<String, Object> result = OBJECT_MAPPER.readValue(jsonString, typeReference);
            return result != null ? result : Collections.emptyMap();
        } catch (Exception e) {
            log.error("JSON字符串转换为Map失败: {}", jsonString, e);
            return Collections.emptyMap();
        }
    }
} 