package com.sankuai.dzusergrowth.operation.infrastructure.dal.po;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: ai_admin_evaluation_task
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationTaskPO {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: source_dataset_id
     *   说明: 资源测的数据集ID
     */
    private Long sourceDatasetId;

    /**
     *   字段: task_name
     *   说明: 任务名称
     */
    private String taskName;

    /**
     *   字段: evaluation_type
     *   说明: 评测类型: 1-手动评测; 2-自动检验
     */
    private Integer evaluationType;

    /**
     *   字段: evaluation_config
     *   说明: 评测任务配置
     */
    private String evaluationConfig;

    /**
     *   字段: evaluation_dataset_id
     *   说明: 产出数据集ID
     */
    private Long evaluationDatasetId;

    /**
     *   字段: status
     *   说明: 任务执行状态
     */
    private Integer status;

    /**
     *   字段: evaluation_data
     *   说明: 统计结果
     */
    private String evaluationData;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: creator_id
     *   说明: 创建人ID
     */
    private Long creatorId;

    /**
     *   字段: updater_id
     *   说明: 更新人ID
     */
    private Long updaterId;

    /**
     *   字段: description
     *   说明: 任务描述
     */
    private String description;
}