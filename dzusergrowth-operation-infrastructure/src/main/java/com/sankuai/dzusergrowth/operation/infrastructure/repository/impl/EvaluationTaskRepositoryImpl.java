package com.sankuai.dzusergrowth.operation.infrastructure.repository.impl;

import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.sankuai.dzusergrowth.operation.domain.model.evaluation.EvaluationTaskDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.EvaluationTaskQuery;
import com.sankuai.dzusergrowth.operation.domain.repository.EvaluationTaskRepository;
import com.sankuai.dzusergrowth.operation.infrastructure.converter.EvaluationTaskConverter;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.example.EvaluationTaskPOExample;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.mapper.EvaluationTaskPOMapper;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.po.EvaluationTaskPO;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 评测任务仓储实现类
 */
@Repository
public class EvaluationTaskRepositoryImpl implements EvaluationTaskRepository {

    @Resource
    private EvaluationTaskPOMapper evaluationTaskMapper;

    /**
     * 创建新的评测任务
     *
     * @param evaluationTaskDO 评测任务信息DO
     */
    @Override
    public void createEvaluationTask(EvaluationTaskDO evaluationTaskDO) {
        if (evaluationTaskDO == null) {
            throw new IllegalArgumentException("评测任务对象不能为空");
        }
        
        // 根据EvaluationTaskDO构建EvaluationTask PO对象
        EvaluationTaskPO evaluationTaskPO = EvaluationTaskConverter.convertDOToPO(evaluationTaskDO);
        // 保存评测任务到数据库
        evaluationTaskMapper.insertSelective(evaluationTaskPO);
        
        // 设置生成的ID
        evaluationTaskDO.setId(evaluationTaskPO.getId());
    }

    /**
     * 根据ID获取评测任务
     *
     * @param taskId 任务ID
     * @return 评测任务信息，如不存在则返回null
     */
    @Override
    public EvaluationTaskDO getEvaluationTaskById(Long taskId) {
        if (taskId == null) {
            return null;
        }
        
        EvaluationTaskPOExample example = new EvaluationTaskPOExample();
        example.createCriteria().andIdEqualTo(taskId);

        // 使用ZebraForceMasterHelper强制查询主库
        try {
            ZebraForceMasterHelper.forceMasterInLocalContext();
            List<EvaluationTaskPO> evaluationTaskPOS = evaluationTaskMapper.selectByExample(example);
            if (evaluationTaskPOS == null || evaluationTaskPOS.isEmpty()) {
                return null;
            }

            return EvaluationTaskConverter.convertPOToDO(evaluationTaskPOS.get(0));
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
    }
    
    /**
     * 更新评测任务
     *
     * @param evaluationTaskDO 评测任务DO
     */
    @Override
    public void updateEvaluationTask(EvaluationTaskDO evaluationTaskDO) {
        if (evaluationTaskDO == null) {
            throw new IllegalArgumentException("评测任务对象不能为空");
        }
        
        if (evaluationTaskDO.getId() == null) {
            throw new IllegalArgumentException("评测任务ID不能为空");
        }
        
        // 构建更新对象
        EvaluationTaskPO evaluationTaskPO = EvaluationTaskConverter.convertDOToPO(evaluationTaskDO);
        
        // 构建条件
        EvaluationTaskPOExample example = new EvaluationTaskPOExample();
        example.createCriteria().andIdEqualTo(evaluationTaskDO.getId());
        
        // 执行更新
        int result = evaluationTaskMapper.updateByExampleSelective(evaluationTaskPO, example);
        
        if (result != 1) {
            throw new IllegalStateException("更新评测任务失败，任务ID: " + evaluationTaskDO.getId() + "，可能任务不存在");
        }
    }
    
    /**
     * 删除评测任务
     *
     * @param taskId 任务ID
     */
    @Override
    public void deleteEvaluationTask(Long taskId) {
        if (taskId == null) {
            throw new IllegalArgumentException("评测任务ID不能为空");
        }
        
        EvaluationTaskPOExample example = new EvaluationTaskPOExample();
        example.createCriteria().andIdEqualTo(taskId);
        
        int result = evaluationTaskMapper.deleteByExample(example);
        
        if (result != 1) {
            throw new IllegalStateException("删除评测任务失败，任务ID: " + taskId + "，可能任务不存在");
        }
    }
    
    /**
     * 批量查询评测任务
     *
     * @param query 评测任务查询条件
     * @return 评测任务DO列表
     */
    @Override
    public List<EvaluationTaskDO> queryEvaluationTask(EvaluationTaskQuery query) {
        if (query == null) {
            throw new IllegalArgumentException("查询条件不能为空");
        }
        
        EvaluationTaskPOExample example = new EvaluationTaskPOExample();
        EvaluationTaskPOExample.Criteria criteria = example.createCriteria();
        
        // 构建查询条件
        if (!CollectionUtils.isEmpty(query.getTaskIds())) {
            criteria.andIdIn(query.getTaskIds());
        }
        
        if (query.getSourceDatasetId() != null) {
            criteria.andSourceDatasetIdEqualTo(query.getSourceDatasetId());
        }
        
        if (StringUtils.hasText(query.getTaskName())) {
            criteria.andTaskNameLike("%" + query.getTaskName() + "%");
        }
        
        if (query.getEvaluationType() != null) {
            criteria.andEvaluationTypeEqualTo(query.getEvaluationType());
        }
        
        if (query.getEvaluationDatasetId() != null) {
            criteria.andEvaluationDatasetIdEqualTo(query.getEvaluationDatasetId());
        }
        
        if (query.getStatus() != null) {
            criteria.andStatusEqualTo(query.getStatus());
        }
        
        if (query.getCreatorId() != null) {
            criteria.andCreatorIdEqualTo(query.getCreatorId());
        }
        
        if (query.getAddTimeStart() != null) {
            criteria.andAddTimeGreaterThanOrEqualTo(query.getAddTimeStart());
        }
        
        if (query.getAddTimeEnd() != null) {
            criteria.andAddTimeLessThanOrEqualTo(query.getAddTimeEnd());
        }
        
        // 设置排序
        if (StringUtils.hasText(query.getOrderBy())) {
            String orderClause = query.getOrderBy();
            if (StringUtils.hasText(query.getOrderDirection())) {
                orderClause += " " + query.getOrderDirection();
            }
            example.setOrderByClause(orderClause);
        } else {
            example.setOrderByClause("add_time DESC");
        }
        
        // 处理分页
        if (query.getPageSize() != null && query.getPageSize() > 0) {
            int pageNum = query.getPageNum() != null && query.getPageNum() > 0 ? query.getPageNum() : 1;
            int offset = (pageNum - 1) * query.getPageSize();
            example.setOffset(offset);
            example.setRows(query.getPageSize());
        }
        
        List<EvaluationTaskPO> evaluationTaskPOS = evaluationTaskMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(evaluationTaskPOS)) {
            return Collections.emptyList();
        }
        
        return evaluationTaskPOS.stream()
                .map(EvaluationTaskConverter::convertPOToDO)
                .collect(Collectors.toList());
    }
} 