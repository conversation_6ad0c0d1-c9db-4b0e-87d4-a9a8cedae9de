package com.sankuai.dzusergrowth.operation.infrastructure.dal.po;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: ai_admin_annotation_data_item
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationDataItemPO {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: dataset_id
     *   说明: 数据集ID
     */
    private Long datasetId;

    /**
     *   字段: data_unique_key
     *   说明: 数据唯一键-标识: 比如TraceID，活动ID等
     */
    private String dataUniqueKey;

    /**
     *   字段: task_id
     *   说明: 任务
     */
    private Long taskId;

    /**
     *   字段: status
     *   说明: 状态
     */
    private Integer status;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: creator_id
     *   说明: 创建人ID
     */
    private Long creatorId;

    /**
     *   字段: updater_id
     *   说明: 更新人ID
     */
    private Long updaterId;
}