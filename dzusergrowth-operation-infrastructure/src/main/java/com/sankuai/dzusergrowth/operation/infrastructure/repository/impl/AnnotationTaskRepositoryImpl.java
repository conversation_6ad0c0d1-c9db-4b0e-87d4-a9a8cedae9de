package com.sankuai.dzusergrowth.operation.infrastructure.repository.impl;

import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationTaskDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.AnnotationTaskQuery;
import com.sankuai.dzusergrowth.operation.domain.repository.AnnotationTaskRepository;
import com.sankuai.dzusergrowth.operation.infrastructure.converter.AnnotationTaskConverter;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.example.AnnotationTaskPOExample;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.mapper.AnnotationTaskPOMapper;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.po.AnnotationTaskPO;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标注任务仓储实现类
 */
@Repository
public class AnnotationTaskRepositoryImpl implements AnnotationTaskRepository {

    @Resource
    private AnnotationTaskPOMapper annotationTaskMapper;

    /**
     * 创建新的标注任务
     *
     * @param annotationTaskDO 标注任务信息DO
     */
    @Override
    public void createAnnotationTask(AnnotationTaskDO annotationTaskDO) {
        if (annotationTaskDO == null) {
            throw new IllegalArgumentException("标注任务对象不能为空");
        }
        
        // 根据AnnotationTaskDO构建AnnotationTask PO对象
        AnnotationTaskPO annotationTaskPO = AnnotationTaskConverter.convertDOToPO(annotationTaskDO);
        // 保存标注任务到数据库
        annotationTaskMapper.insertSelective(annotationTaskPO);
        
        // 设置生成的ID
        annotationTaskDO.setId(annotationTaskPO.getId());
    }

    /**
     * 根据ID获取标注任务
     *
     * @param taskId 任务ID
     * @return 标注任务信息，如不存在则返回null
     */
    @Override
    public AnnotationTaskDO getAnnotationTaskById(Long taskId) {
        if (taskId == null) {
            return null;
        }
        
        AnnotationTaskPOExample example = new AnnotationTaskPOExample();
        example.createCriteria().andIdEqualTo(taskId);

        // 使用ZebraForceMasterHelper强制查询主库
        try {
            ZebraForceMasterHelper.forceMasterInLocalContext();
            List<AnnotationTaskPO> annotationTaskPOS = annotationTaskMapper.selectByExample(example);
            if (annotationTaskPOS == null || annotationTaskPOS.isEmpty()) {
                return null;
            }

            return AnnotationTaskConverter.convertPOToDO(annotationTaskPOS.get(0));
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
    }
    
    /**
     * 更新标注任务
     *
     * @param annotationTaskDO 标注任务DO
     */
    @Override
    public void updateAnnotationTask(AnnotationTaskDO annotationTaskDO) {
        if (annotationTaskDO == null) {
            throw new IllegalArgumentException("标注任务对象不能为空");
        }
        
        if (annotationTaskDO.getId() == null) {
            throw new IllegalArgumentException("标注任务ID不能为空");
        }
        
        // 构建更新对象
        AnnotationTaskPO annotationTaskPO = AnnotationTaskConverter.convertDOToPO(annotationTaskDO);
        
        // 构建条件
        AnnotationTaskPOExample example = new AnnotationTaskPOExample();
        example.createCriteria().andIdEqualTo(annotationTaskDO.getId());
        
        // 执行更新
        int result = annotationTaskMapper.updateByExampleSelective(annotationTaskPO, example);
        
        if (result != 1) {
            throw new IllegalStateException("更新标注任务失败，任务ID: " + annotationTaskDO.getId() + "，可能任务不存在");
        }
    }
    
    /**
     * 删除标注任务
     *
     * @param taskId 任务ID
     */
    @Override
    public void deleteAnnotationTask(Long taskId) {
        if (taskId == null) {
            throw new IllegalArgumentException("标注任务ID不能为空");
        }
        
        AnnotationTaskPOExample example = new AnnotationTaskPOExample();
        example.createCriteria().andIdEqualTo(taskId);
        
        int result = annotationTaskMapper.deleteByExample(example);
        
        if (result != 1) {
            throw new IllegalStateException("删除标注任务失败，任务ID: " + taskId + "，可能任务不存在");
        }
    }
    
    /**
     * 批量查询标注任务
     *
     * @param query 标注任务查询条件
     * @return 标注任务DO列表
     */
    @Override
    public List<AnnotationTaskDO> queryAnnotationTask(AnnotationTaskQuery query) {
        if (query == null) {
            throw new IllegalArgumentException("查询条件不能为空");
        }
        
        AnnotationTaskPOExample example = new AnnotationTaskPOExample();
        AnnotationTaskPOExample.Criteria criteria = example.createCriteria();
        
        // 构建查询条件
        if (!CollectionUtils.isEmpty(query.getTaskIds())) {
            criteria.andIdIn(query.getTaskIds());
        }
        
        if (query.getAnnotationDatasetId() != null) {
            criteria.andAnnotationDatasetIdEqualTo(query.getAnnotationDatasetId());
        }
        
        if (StringUtils.hasText(query.getTaskName())) {
            criteria.andTaskNameLike("%" + query.getTaskName() + "%");
        }
        
        if (query.getEffectiveAnnotationType() != null) {
            criteria.andAnnotationTypeEqualTo(query.getEffectiveAnnotationType());
        }
        
        if (query.getTaskModel() != null) {
            criteria.andTaskModelEqualTo(query.getTaskModel());
        }
        
        if (query.getCreatorId() != null) {
            criteria.andCreatorIdEqualTo(query.getCreatorId());
        }
        
        if (query.getAddTimeStart() != null) {
            criteria.andAddTimeGreaterThanOrEqualTo(query.getAddTimeStart());
        }
        
        if (query.getAddTimeEnd() != null) {
            criteria.andAddTimeLessThanOrEqualTo(query.getAddTimeEnd());
        }
        
        // 设置排序
        if (StringUtils.hasText(query.getOrderBy())) {
            String orderClause = query.getOrderBy();
            if (StringUtils.hasText(query.getOrderDirection())) {
                orderClause += " " + query.getOrderDirection();
            }
            example.setOrderByClause(orderClause);
        } else {
            example.setOrderByClause("add_time DESC");
        }
        
        // 处理分页
        if (query.getPageSize() != null && query.getPageSize() > 0) {
            int pageNum = query.getPageNum() != null && query.getPageNum() > 0 ? query.getPageNum() : 1;
            int offset = (pageNum - 1) * query.getPageSize();
            example.setOffset(offset);
            example.setRows(query.getPageSize());
        }
        
        List<AnnotationTaskPO> annotationTaskPOS = annotationTaskMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(annotationTaskPOS)) {
            return Collections.emptyList();
        }
        
        return annotationTaskPOS.stream()
                .map(AnnotationTaskConverter::convertPOToDO)
                .collect(Collectors.toList());
    }
} 