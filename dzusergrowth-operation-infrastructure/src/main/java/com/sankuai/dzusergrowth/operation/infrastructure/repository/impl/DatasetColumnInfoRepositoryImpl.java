package com.sankuai.dzusergrowth.operation.infrastructure.repository.impl;

import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetColumnInfoDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.DatasetColumnInfoQuery;
import com.sankuai.dzusergrowth.operation.domain.repository.DatasetColumnInfoRepository;
import com.sankuai.dzusergrowth.operation.infrastructure.converter.DatasetColumnInfoConverter;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DatasetColumnInfoPOExample;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.mapper.DatasetColumnInfoPOMapper;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DatasetColumnInfoPO;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据集列信息仓储实现类
 */
@Repository
public class DatasetColumnInfoRepositoryImpl implements DatasetColumnInfoRepository {

    @Resource
    private DatasetColumnInfoPOMapper datasetColumnInfoMapper;

    /**
     * 创建新的数据集列信息
     *
     * @param columnInfoDO 数据集列信息DO
     */
    @Override
    public void createDatasetColumnInfo(DatasetColumnInfoDO columnInfoDO) {
        if (columnInfoDO == null) {
            throw new IllegalArgumentException("数据集列信息对象不能为空");
        }
        
        // 根据DatasetColumnInfoDO构建DatasetColumnInfo PO对象
        DatasetColumnInfoPO columnInfoPO = DatasetColumnInfoConverter.convertDOToPO(columnInfoDO);
        // 保存数据集列信息到数据库
        datasetColumnInfoMapper.insertSelective(columnInfoPO);
        
        // 设置生成的ID
        columnInfoDO.setId(columnInfoPO.getId());
    }

    /**
     * 根据ID获取数据集列信息
     *
     * @param columnId 列ID
     * @return 数据集列信息，如不存在则返回null
     */
    @Override
    public DatasetColumnInfoDO getDatasetColumnInfoById(Long columnId) {
        if (columnId == null) {
            return null;
        }
        
        DatasetColumnInfoPOExample example = new DatasetColumnInfoPOExample();
        example.createCriteria().andIdEqualTo(columnId);

        // 使用ZebraForceMasterHelper强制查询主库
        try {
            ZebraForceMasterHelper.forceMasterInLocalContext();
            List<DatasetColumnInfoPO> columnInfos = datasetColumnInfoMapper.selectByExample(example);
            if (columnInfos == null || columnInfos.isEmpty()) {
                return null;
            }

            return DatasetColumnInfoConverter.convertPOToDO(columnInfos.get(0));
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
    }
    
    /**
     * 更新数据集列信息
     *
     * @param columnInfoDO 数据集列信息DO
     */
    @Override
    public void updateDatasetColumnInfo(DatasetColumnInfoDO columnInfoDO) {
        if (columnInfoDO == null) {
            throw new IllegalArgumentException("数据集列信息对象不能为空");
        }
        
        if (columnInfoDO.getId() == null) {
            throw new IllegalArgumentException("数据集列信息ID不能为空");
        }
        
        // 构建更新对象
        DatasetColumnInfoPO columnInfoPO = DatasetColumnInfoConverter.convertDOToPO(columnInfoDO);
        
        // 构建条件
        DatasetColumnInfoPOExample example = new DatasetColumnInfoPOExample();
        example.createCriteria().andIdEqualTo(columnInfoDO.getId());
        
        // 执行更新
        int result = datasetColumnInfoMapper.updateByExampleSelective(columnInfoPO, example);
        
        if (result != 1) {
            throw new IllegalStateException("更新数据集列信息失败，列ID: " + columnInfoDO.getId() + "，可能列信息不存在");
        }
    }
    
    /**
     * 删除数据集列信息
     *
     * @param columnId 列ID
     */
    @Override
    public void deleteDatasetColumnInfo(Long columnId) {
        if (columnId == null) {
            throw new IllegalArgumentException("数据集列信息ID不能为空");
        }
        
        DatasetColumnInfoPOExample example = new DatasetColumnInfoPOExample();
        example.createCriteria().andIdEqualTo(columnId);
        
        int result = datasetColumnInfoMapper.deleteByExample(example);
        
        if (result != 1) {
            throw new IllegalStateException("删除数据集列信息失败，列ID: " + columnId + "，可能列信息不存在");
        }
    }
    
    /**
     * 批量查询数据集列信息
     *
     * @param query 数据集列信息查询条件
     * @return 数据集列信息DO列表
     */
    @Override
    public List<DatasetColumnInfoDO> queryDatasetColumnInfo(DatasetColumnInfoQuery query) {
        if (query == null) {
            throw new IllegalArgumentException("查询条件不能为空");
        }
        
        DatasetColumnInfoPOExample example = new DatasetColumnInfoPOExample();
        DatasetColumnInfoPOExample.Criteria criteria = example.createCriteria();
        
        // 构建查询条件
        if (!CollectionUtils.isEmpty(query.getColumnIds())) {
            criteria.andIdIn(query.getColumnIds());
        }
        
        if (query.getDatasetId() != null) {
            criteria.andDatasetIdEqualTo(query.getDatasetId());
        }
        
        if (!CollectionUtils.isEmpty(query.getDatasetIds())) {
            criteria.andDatasetIdIn(query.getDatasetIds());
        }
        
        if (StringUtils.hasText(query.getName())) {
            criteria.andNameLike("%" + query.getName() + "%");
        }
        
        if (query.getDataType() != null) {
            criteria.andDataTypeEqualTo(query.getDataType());
        }
        
        if (query.getColumnType() != null) {
            criteria.andColumnTypeEqualTo(query.getColumnType());
        }
        
        if (query.getCreatorId() != null) {
            criteria.andCreatorIdEqualTo(query.getCreatorId());
        }
        
        if (query.getAddTimeStart() != null) {
            criteria.andAddTimeGreaterThanOrEqualTo(query.getAddTimeStart());
        }
        
        if (query.getAddTimeEnd() != null) {
            criteria.andAddTimeLessThanOrEqualTo(query.getAddTimeEnd());
        }
        
        // 设置排序
        if (StringUtils.hasText(query.getOrderBy())) {
            String orderClause = query.getOrderBy();
            if (StringUtils.hasText(query.getOrderDirection())) {
                orderClause += " " + query.getOrderDirection();
            }
            example.setOrderByClause(orderClause);
        } else {
            example.setOrderByClause("add_time DESC");
        }
        
        // 处理分页
        if (query.getPageSize() != null && query.getPageSize() > 0) {
            int pageNum = query.getPageNum() != null && query.getPageNum() > 0 ? query.getPageNum() : 1;
            int offset = (pageNum - 1) * query.getPageSize();
            example.setOffset(offset);
            example.setRows(query.getPageSize());
        }
        
        List<DatasetColumnInfoPO> columnInfos = datasetColumnInfoMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(columnInfos)) {
            return Collections.emptyList();
        }
        
        return columnInfos.stream()
                .map(DatasetColumnInfoConverter::convertPOToDO)
                .collect(Collectors.toList());
    }
    
    /**
     * 检查指定数据集中是否存在同名列
     *
     * @param datasetId 数据集ID
     * @param columnName 列名
     * @param excludeId 排除的列ID（用于更新时检查重名，可为null）
     * @return 是否存在同名列
     */
    @Override
    public boolean isColumnNameExists(Long datasetId, String columnName, Long excludeId) {
        if (datasetId == null) {
            throw new IllegalArgumentException("数据集ID不能为空");
        }
        
        if (!StringUtils.hasText(columnName)) {
            throw new IllegalArgumentException("列名不能为空");
        }
        
        DatasetColumnInfoPOExample example = new DatasetColumnInfoPOExample();
        DatasetColumnInfoPOExample.Criteria criteria = example.createCriteria();
        
        // 精确匹配数据集ID和列名
        criteria.andDatasetIdEqualTo(datasetId);
        criteria.andNameEqualTo(columnName);
        
        // 排除指定ID（用于更新时检查重名）
        if (excludeId != null) {
            criteria.andIdNotEqualTo(excludeId);
        }
        
        List<DatasetColumnInfoPO> columnInfos = datasetColumnInfoMapper.selectByExample(example);
        
        return !CollectionUtils.isEmpty(columnInfos);
    }
} 