package com.sankuai.dzusergrowth.operation.infrastructure.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.dzusergrowth.operation.api.enums.DatasetTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.DataGranularityEnum;
import com.sankuai.dzusergrowth.operation.api.enums.GenerateTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.TaskStatusEnum;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetGenerateConfig;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DatasetPO;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据集转换器
 */
@Slf4j
public class DatasetConverter {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();


    /**
     * DO转PO
     */
    public static DatasetPO convertDOToPO(DatasetDO datasetDO) {
        if (datasetDO == null) {
            return null;
        }
        
        DatasetPO datasetPO = new DatasetPO();
        datasetPO.setId(datasetDO.getId());
        
        // 枚举转换为Integer
        if (datasetDO.getDatasetType() != null) {
            datasetPO.setDatasetType(datasetDO.getDatasetType().getCode());
        }
        if (datasetDO.getDataGranularity() != null) {
            datasetPO.setDataGranularity(datasetDO.getDataGranularity().getCode());
        }
        if (datasetDO.getGenerateType() != null) {
            datasetPO.setGenerateType(datasetDO.getGenerateType().getCode());
        }
        if (datasetDO.getTaskStatus() != null) {
            datasetPO.setTaskStatus(datasetDO.getTaskStatus().getCode());
        }
        
        datasetPO.setName(datasetDO.getName());
        
        // 将配置对象转换为JSON字符串
        if (datasetDO.getGenerateConfig() != null) {
            try {
                String configJson = OBJECT_MAPPER.writeValueAsString(datasetDO.getGenerateConfig());
                datasetPO.setGenerateConfig(configJson);
            } catch (JsonProcessingException e) {
                log.error("Failed to convert generateConfig to JSON for dataset: {}", datasetDO.getId(), e);
                datasetPO.setGenerateConfig(null);
            }
        }
        
        datasetPO.setCreatorId(datasetDO.getCreatorId());
        datasetPO.setUpdaterId(datasetDO.getUpdaterId());
        datasetPO.setDescription(datasetDO.getDescription());
        datasetPO.setAddTime(datasetDO.getAddTime());
        datasetPO.setUpdateTime(datasetDO.getUpdateTime());
        
        return datasetPO;
    }

    /**
     * PO转DO
     */
    public static DatasetDO convertPOToDO(DatasetPO datasetPO) {
        if (datasetPO == null) {
            return null;
        }
        
        DatasetDO.DatasetDOBuilder builder = DatasetDO.builder()
                .id(datasetPO.getId())
                .name(datasetPO.getName())
                .creatorId(datasetPO.getCreatorId())
                .updaterId(datasetPO.getUpdaterId())
                .description(datasetPO.getDescription())
                .addTime(datasetPO.getAddTime())
                .updateTime(datasetPO.getUpdateTime());
        
        // Integer转换为枚举
        if (datasetPO.getDatasetType() != null) {
            DatasetTypeEnum datasetType = DatasetTypeEnum.fromCode(datasetPO.getDatasetType());
            builder.datasetType(datasetType);
        }
        if (datasetPO.getDataGranularity() != null) {
            DataGranularityEnum dataGranularity = DataGranularityEnum.fromCode(datasetPO.getDataGranularity());
            builder.dataGranularity(dataGranularity);
        }
        if (datasetPO.getGenerateType() != null) {
            GenerateTypeEnum generateType = GenerateTypeEnum.fromCode(datasetPO.getGenerateType());
            builder.generateType(generateType);
        }
        if (datasetPO.getTaskStatus() != null) {
            TaskStatusEnum taskStatus = TaskStatusEnum.fromCode(datasetPO.getTaskStatus());
            builder.taskStatus(taskStatus);
        }
        
        // 将JSON字符串转换为配置对象
        if (datasetPO.getGenerateConfig() != null && !datasetPO.getGenerateConfig().trim().isEmpty()) {
            try {
                DatasetGenerateConfig config = OBJECT_MAPPER.readValue(
                    datasetPO.getGenerateConfig(), 
                    DatasetGenerateConfig.class
                );
                builder.generateConfig(config);
            } catch (JsonProcessingException e) {
                log.error("Failed to parse generateConfig JSON for dataset: {}", datasetPO.getId(), e);
                // 解析失败时，只设置JSON字符串，配置对象为null
                builder.generateConfig(null);
            }
        }
        
        return builder.build();
    }
} 