<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<!-- 配置生成器 -->
<generatorConfiguration>
    <!-- 配置对象环境，注意：设置defaultModelType=flat后在含有blob类型(如数据库的text/longtext等)字段时只会生成一个entity，在调用mapper时只有带WithBLOBs后缀的方法才会将blob字段查询出来-->
    <context id="goods" targetRuntime="MyBatis3" defaultModelType="flat">
        <!-- 配置插件 -->
        <!-- 生成的entity实体引入lombok注解 Getter,Setter,Builder -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.LombokPlugin"/>
        <!--生成通用sql方法类，包含通用方法。共MdpMapperPlugin、MdpSimpleMapperPlugin、MdpMixedMapperPlugin
        3个插件，根据诉求决定使用哪个插件，具体区别见 https://km.sankuai.com/page/424829078#id-4.1MDP%E6%8F%90%E4%BE%9B%E7%9A%84%E6%8F%92%E4%BB%B6 -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMapperPlugin"/>
        <!--<plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpSimpleMapperPlugin"/>-->
        <!--<plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMixedMapperPlugin"/>-->
        <!-- 每次执行插件生成的 xml 时通用的方法会覆盖的 -->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
        <!-- 生成批量插入方法插件，默认不需要，需要时配置此插件。使用条件：
        Context targetRuntime="Mybatis3" ; javaClientGenerator type="XMLMAPPER、MIXEDMAPPER"-->
<!--        <plugin type="com.meituan.mdp.mybatis.generator.plugins.BatchInsertPlugin"/>-->
        <!--分页插件，默认不开启。使用条件：Context targetRuntime="Mybatis3" ; javaClientGenerator
        type="XMLMAPPER、MIXEDMAPPER"-->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.LimitPlugin"/>
        <!-- targetRuntime="Mybatis3"时需要，Example类存储路径 -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.ExampleTargetPlugin">
            <property name="targetPackage" value="com.sankuai.dzusergrowth.operation.infrastructure.dal.example"/>
        </plugin>

        <plugin type="org.mybatis.generator.plugins.RowBoundsPlugin"/>

        <!-- 配置注释生成器 -->
        <!-- 从数据库中的字段的comment做为生成entity的属性注释 -->
        <commentGenerator type="com.meituan.mdp.mybatis.generator.internal.RemarksCommentGenerator">
            <property name="suppressAllComments" value="true"/>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>


        <!-- MDP1.8.X支持直接配置jdbcref，配置了zebra元素就不用配置jdbcConnection了，两者只能配置一个 -->
        <!-- jdbcRef 和 bladeJdbcRef 根据需要配置一个 -->
<!--        <zebra jdbcRef="gmkteventmanage"/>-->
<!--        <zebra bladeJdbcRef="替换为服务对应的jdbcRef" appkey="服务的appkey"/>-->

        <!-- MDP1.8.X以下必须配置的项，连接数据库 -->
        <!--使用前替换数据库名,账号密码-->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="****************************************************************"
                        userId="rds_test"
                        password="ov65Tk$ZDZ#x5t">
        </jdbcConnection>

        <!-- 指定JDBC类型和Java类型时如何转换的 -->
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- 配置生成的实体类位置 -->
        <javaModelGenerator targetPackage="com.sankuai.dzusergrowth.operation.infrastructure.dal.po" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- 配置映射位置 -->
        <sqlMapGenerator targetPackage="mappers" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!-- 配置接口位置 -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sankuai.dzusergrowth.operation.infrastructure.dal.mapper" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!--配置需要生成的表-->
        <!-- AI管理相关表配置 -->
        <table tableName="ai_admin_annotation_task" domainObjectName="AnnotationTaskPO">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
        </table>

        <table tableName="ai_admin_annotation_data_item" domainObjectName="AnnotationDataItemPO">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
        </table>

        <table tableName="ai_admin_annotation_label" domainObjectName="AnnotationLabelPO">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
        </table>

        <table tableName="ai_admin_dataset" domainObjectName="DatasetPO">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
        </table>

        <table tableName="ai_admin_data_item" domainObjectName="DataItemPO">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
        </table>

        <table tableName="ai_admin_dataset_column_info" domainObjectName="DatasetColumnInfoPO">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
        </table>

        <table tableName="ai_admin_evaluation_task" domainObjectName="EvaluationTaskPO">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
        </table>

    </context>

</generatorConfiguration>
