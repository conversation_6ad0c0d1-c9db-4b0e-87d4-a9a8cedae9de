<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzusergrowth.operation.infrastructure.dal.mapper.DatasetPOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DatasetPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dataset_type" jdbcType="INTEGER" property="datasetType" />
    <result column="data_granularity" jdbcType="INTEGER" property="dataGranularity" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="generate_type" jdbcType="INTEGER" property="generateType" />
    <result column="generate_config" jdbcType="CHAR" property="generateConfig" />
    <result column="task_status" jdbcType="INTEGER" property="taskStatus" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DatasetPO">
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, dataset_type, data_granularity, name, generate_type, generate_config, task_status, 
    add_time, update_time, creator_id, updater_id
  </sql>
  <sql id="Blob_Column_List">
    description
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DatasetPOExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ai_admin_dataset
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DatasetPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ai_admin_dataset
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ai_admin_dataset
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ai_admin_dataset
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DatasetPOExample">
    delete from ai_admin_dataset
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DatasetPO" useGeneratedKeys="true">
    insert into ai_admin_dataset (dataset_type, data_granularity, name, 
      generate_type, generate_config, task_status, 
      add_time, update_time, creator_id, 
      updater_id, description)
    values (#{datasetType,jdbcType=INTEGER}, #{dataGranularity,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, 
      #{generateType,jdbcType=INTEGER}, #{generateConfig,jdbcType=CHAR}, #{taskStatus,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{creatorId,jdbcType=BIGINT}, 
      #{updaterId,jdbcType=BIGINT}, #{description,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DatasetPO" useGeneratedKeys="true">
    insert into ai_admin_dataset
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="datasetType != null">
        dataset_type,
      </if>
      <if test="dataGranularity != null">
        data_granularity,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="generateType != null">
        generate_type,
      </if>
      <if test="generateConfig != null">
        generate_config,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="description != null">
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="datasetType != null">
        #{datasetType,jdbcType=INTEGER},
      </if>
      <if test="dataGranularity != null">
        #{dataGranularity,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="generateType != null">
        #{generateType,jdbcType=INTEGER},
      </if>
      <if test="generateConfig != null">
        #{generateConfig,jdbcType=CHAR},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DatasetPOExample" resultType="java.lang.Long">
    select count(*) from ai_admin_dataset
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ai_admin_dataset
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.datasetType != null">
        dataset_type = #{record.datasetType,jdbcType=INTEGER},
      </if>
      <if test="record.dataGranularity != null">
        data_granularity = #{record.dataGranularity,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.generateType != null">
        generate_type = #{record.generateType,jdbcType=INTEGER},
      </if>
      <if test="record.generateConfig != null">
        generate_config = #{record.generateConfig,jdbcType=CHAR},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=BIGINT},
      </if>
      <if test="record.updaterId != null">
        updater_id = #{record.updaterId,jdbcType=BIGINT},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update ai_admin_dataset
    set id = #{record.id,jdbcType=BIGINT},
      dataset_type = #{record.datasetType,jdbcType=INTEGER},
      data_granularity = #{record.dataGranularity,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      generate_type = #{record.generateType,jdbcType=INTEGER},
      generate_config = #{record.generateConfig,jdbcType=CHAR},
      task_status = #{record.taskStatus,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      creator_id = #{record.creatorId,jdbcType=BIGINT},
      updater_id = #{record.updaterId,jdbcType=BIGINT},
      description = #{record.description,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ai_admin_dataset
    set id = #{record.id,jdbcType=BIGINT},
      dataset_type = #{record.datasetType,jdbcType=INTEGER},
      data_granularity = #{record.dataGranularity,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      generate_type = #{record.generateType,jdbcType=INTEGER},
      generate_config = #{record.generateConfig,jdbcType=CHAR},
      task_status = #{record.taskStatus,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      creator_id = #{record.creatorId,jdbcType=BIGINT},
      updater_id = #{record.updaterId,jdbcType=BIGINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DatasetPO">
    update ai_admin_dataset
    <set>
      <if test="datasetType != null">
        dataset_type = #{datasetType,jdbcType=INTEGER},
      </if>
      <if test="dataGranularity != null">
        data_granularity = #{dataGranularity,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="generateType != null">
        generate_type = #{generateType,jdbcType=INTEGER},
      </if>
      <if test="generateConfig != null">
        generate_config = #{generateConfig,jdbcType=CHAR},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DatasetPO">
    update ai_admin_dataset
    set dataset_type = #{datasetType,jdbcType=INTEGER},
      data_granularity = #{dataGranularity,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      generate_type = #{generateType,jdbcType=INTEGER},
      generate_config = #{generateConfig,jdbcType=CHAR},
      task_status = #{taskStatus,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator_id = #{creatorId,jdbcType=BIGINT},
      updater_id = #{updaterId,jdbcType=BIGINT},
      description = #{description,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DatasetPO">
    update ai_admin_dataset
    set dataset_type = #{datasetType,jdbcType=INTEGER},
      data_granularity = #{dataGranularity,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      generate_type = #{generateType,jdbcType=INTEGER},
      generate_config = #{generateConfig,jdbcType=CHAR},
      task_status = #{taskStatus,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator_id = #{creatorId,jdbcType=BIGINT},
      updater_id = #{updaterId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DatasetPOExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ai_admin_dataset
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DatasetPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ai_admin_dataset
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
</mapper>