<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzusergrowth.operation.infrastructure.dal.mapper.DataItemPOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DataItemPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dataset_id" jdbcType="BIGINT" property="datasetId" />
    <result column="data_unique_key" jdbcType="VARCHAR" property="dataUniqueKey" />
    <result column="data" jdbcType="CHAR" property="data" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, dataset_id, data_unique_key, data, is_deleted, add_time, update_time, creator_id, 
    updater_id
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DataItemPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ai_admin_data_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ai_admin_data_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ai_admin_data_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DataItemPOExample">
    delete from ai_admin_data_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DataItemPO" useGeneratedKeys="true">
    insert into ai_admin_data_item (dataset_id, data_unique_key, data, 
      is_deleted, add_time, update_time, 
      creator_id, updater_id)
    values (#{datasetId,jdbcType=BIGINT}, #{dataUniqueKey,jdbcType=VARCHAR}, #{data,jdbcType=CHAR}, 
      #{isDeleted,jdbcType=BIT}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{creatorId,jdbcType=BIGINT}, #{updaterId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DataItemPO" useGeneratedKeys="true">
    insert into ai_admin_data_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="datasetId != null">
        dataset_id,
      </if>
      <if test="dataUniqueKey != null">
        data_unique_key,
      </if>
      <if test="data != null">
        data,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="datasetId != null">
        #{datasetId,jdbcType=BIGINT},
      </if>
      <if test="dataUniqueKey != null">
        #{dataUniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="data != null">
        #{data,jdbcType=CHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DataItemPOExample" resultType="java.lang.Long">
    select count(*) from ai_admin_data_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ai_admin_data_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.datasetId != null">
        dataset_id = #{record.datasetId,jdbcType=BIGINT},
      </if>
      <if test="record.dataUniqueKey != null">
        data_unique_key = #{record.dataUniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="record.data != null">
        data = #{record.data,jdbcType=CHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=BIGINT},
      </if>
      <if test="record.updaterId != null">
        updater_id = #{record.updaterId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ai_admin_data_item
    set id = #{record.id,jdbcType=BIGINT},
      dataset_id = #{record.datasetId,jdbcType=BIGINT},
      data_unique_key = #{record.dataUniqueKey,jdbcType=VARCHAR},
      data = #{record.data,jdbcType=CHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      creator_id = #{record.creatorId,jdbcType=BIGINT},
      updater_id = #{record.updaterId,jdbcType=BIGINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DataItemPO">
    update ai_admin_data_item
    <set>
      <if test="datasetId != null">
        dataset_id = #{datasetId,jdbcType=BIGINT},
      </if>
      <if test="dataUniqueKey != null">
        data_unique_key = #{dataUniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="data != null">
        data = #{data,jdbcType=CHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DataItemPO">
    update ai_admin_data_item
    set dataset_id = #{datasetId,jdbcType=BIGINT},
      data_unique_key = #{dataUniqueKey,jdbcType=VARCHAR},
      data = #{data,jdbcType=CHAR},
      is_deleted = #{isDeleted,jdbcType=BIT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator_id = #{creatorId,jdbcType=BIGINT},
      updater_id = #{updaterId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DataItemPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ai_admin_data_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
</mapper>