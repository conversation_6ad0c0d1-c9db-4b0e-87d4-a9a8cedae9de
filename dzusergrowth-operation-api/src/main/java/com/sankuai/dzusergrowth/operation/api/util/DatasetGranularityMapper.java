package com.sankuai.dzusergrowth.operation.api.util;

import com.sankuai.dzusergrowth.operation.api.enums.DataGranularityEnum;

/**
 * 数据集粒度映射工具类
 *
 * <AUTHOR> Assistant
 */
public class DatasetGranularityMapper {
    
    /**
     * API粒度值转换为Domain枚举
     *
     * @param apiGranularity API粒度值
     * @return Domain枚举值
     */
    public static DataGranularityEnum fromApiGranularity(String apiGranularity) {
        if (apiGranularity == null) {
            return null;
        }
        
        switch (apiGranularity) {
            case "ACTIVITY_AND_TASK":
                return DataGranularityEnum.ACTIVITY; // 活动粒度包含任务
            case "ASSET":
                return DataGranularityEnum.ASSET;
            default:
                return null;
        }
    }
    
    /**
     * Domain枚举转换为API粒度值
     *
     * @param dataGranularity Domain枚举值
     * @return API粒度值
     */
    public static String toApiGranularity(DataGranularityEnum dataGranularity) {
        if (dataGranularity == null) {
            return null;
        }
        
        switch (dataGranularity) {
            case ACTIVITY:
            case TASK:
                return "ACTIVITY_AND_TASK";
            case ASSET:
                return "ASSET";
            default:
                return "UNKNOWN";
        }
    }
} 