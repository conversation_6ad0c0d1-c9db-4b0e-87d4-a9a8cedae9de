package com.sankuai.dzusergrowth.operation.api.response;

import lombok.Data;
import java.util.Date;

@Data
public class OfflineCodeActivityRecordDTO {
    private Long dpShopId;
    private Long distributorId;
    private Integer dpAccountId;
    private String activityId;
    private String activityName;
    private Long rebateAmount;
    private Integer settleType;
    private Integer status;
    private Date createTime;
    private Date updateTime;
}
