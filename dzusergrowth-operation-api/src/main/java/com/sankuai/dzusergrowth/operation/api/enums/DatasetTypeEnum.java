package com.sankuai.dzusergrowth.operation.api.enums;

import lombok.Getter;

/**
 * 数据集类型枚举
 *
 * <AUTHOR> Assistant
 */
@Getter
public enum DatasetTypeEnum {
    
    /**
     * 原始数据集
     */
    ORIGINAL_DATASET(1, "原始数据集"),
    
    /**
     * 评测结果集
     */
    EVALUATION_RESULT_SET(2, "评测结果集");
    
    /**
     * 类型码
     */
    private final Integer code;
    
    /**
     * 类型描述
     */
    private final String description;
    
    
    /**
     * 构造函数
     *
     * @param code code
     * @param description description
     */
    DatasetTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取code
     *
     * @return code
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取description
     *
     * @return description
     */
    public String getDescription() {
        return description;
    }
    /**
     * 根据类型码获取枚举
     *
     * @param code 类型码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static DatasetTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (DatasetTypeEnum datasetType : values()) {
            if (datasetType.getCode().equals(code)) {
                return datasetType;
            }
        }
        return null;
    }
    
    /**
     * 检查类型码是否有效
     *
     * @param code 类型码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }
} 