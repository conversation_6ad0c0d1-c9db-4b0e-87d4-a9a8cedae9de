package com.sankuai.dzusergrowth.operation.api.service;

import com.sankuai.dzusergrowth.operation.api.common.PageResult;
import com.sankuai.dzusergrowth.operation.api.request.DatasetCreateRequest;
import com.sankuai.dzusergrowth.operation.api.request.DatasetDetailRequest;
import com.sankuai.dzusergrowth.operation.api.request.DatasetListRequest;
import com.sankuai.dzusergrowth.operation.api.request.DatasetUpdateRequest;
import com.sankuai.dzusergrowth.operation.api.response.DatasetDTO;
import com.sankuai.dzusergrowth.operation.api.response.DatasetDetailResponse;

/**
 * 数据集网关服务接口
 * 定义数据集管理的RPC接口
 *
 * <AUTHOR> Assistant
 */
public interface DatasetGatewayService {
    
    /**
     * 获取数据集列表
     * ElinkID: 7371
     * Path: /api/dzusergrowth/agentbackend/dataset/list
     *
     * @param request 查询请求
     * @return 分页数据集列表
     */
    PageResult<DatasetDTO> list(DatasetListRequest request);
    
    /**
     * 新增数据集
     * ElinkID: 7374
     * Path: /api/dzusergrowth/agentbackend/dataset/create
     *
     * @param request 创建请求
     * @return 数据集ID
     */
    String create(DatasetCreateRequest request);
    
    /**
     * 编辑数据集名称描述
     * ElinkID: 7372
     * Path: /api/dzusergrowth/agentbackend/dataset/update
     *
     * @param request 更新请求
     * @return 是否成功
     */
    Boolean update(DatasetUpdateRequest request);
    
    /**
     * 获取数据集详情
     * ElinkID: 7373
     * Path: /api/dzusergrowth/agentbackend/dataset/detail
     *
     * @param request 详情查询请求
     * @return 数据集详情
     */
    DatasetDetailResponse detail(DatasetDetailRequest request);
} 