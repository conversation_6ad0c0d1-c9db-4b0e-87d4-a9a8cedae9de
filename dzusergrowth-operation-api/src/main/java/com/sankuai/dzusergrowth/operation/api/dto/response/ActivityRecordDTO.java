package com.sankuai.dzusergrowth.operation.api.dto.response;

import lombok.Data;
import java.util.Date;

/**
 * 活动记录返回DTO
 */
@Data
public class ActivityRecordDTO {
    /**
     * 点评门店ID
     */
    private Long dpShopId;
    
    /**
     * 活动ID
     */
    private Long activityId;
    
    /**
     * 活动配置名称
     */
    private String activityName;
    
    /**
     * 账号ID
     */
    private Integer accountId;
    
    /**
     * 活动实例状态(1-有效 2-取消报名 0-软删除)
     */
    private Integer status;
    
    /**
     * 美团用户ID
     */
    private Long mtUserId;
    
    /**
     * 结算类型(1-对私 2-对公)
     */
    private Integer settleType;
    
    /**
     * 累计返利金额
     */
    private Long rebateAmount;
    
    /**
     * 最新修改时间
     */
    private Date updateTime;
    
    /**
     * 申请时间
     */
    private Date applyTime;
} 