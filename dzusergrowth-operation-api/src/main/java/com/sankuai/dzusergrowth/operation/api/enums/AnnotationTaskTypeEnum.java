package com.sankuai.dzusergrowth.operation.api.enums;

import lombok.Getter;

/**
 * 标注类型枚举
 *
 * <AUTHOR> Assistant
 */
@Getter
public enum AnnotationTaskTypeEnum {
    
    /**
     * 人工标注
     */
    MANUAL(1, "人工标注"),
    
    /**
     * 自动标注
     */
    AUTO(2, "自动标注");
    
    /**
     * 类型码
     */
    private final Integer code;
    
    /**
     * 类型描述
     */
    private final String description;
    
    
    /**
     * 构造函数
     *
     * @param code code
     * @param description description
     */
    AnnotationTaskTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取code
     *
     * @return code
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取description
     *
     * @return description
     */
    public String getDescription() {
        return description;
    }
    /**
     * 根据类型码获取枚举
     *
     * @param code 类型码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static AnnotationTaskTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (AnnotationTaskTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 检查类型码是否有效
     *
     * @param code 类型码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }
} 