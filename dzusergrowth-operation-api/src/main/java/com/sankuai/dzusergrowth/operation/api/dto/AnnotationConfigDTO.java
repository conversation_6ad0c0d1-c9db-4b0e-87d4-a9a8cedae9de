package com.sankuai.dzusergrowth.operation.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 标注配置DTO
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationConfigDTO {
    
    /**
     * 标注列Id (从数据集列信息表中映射得到)
     */
    private Long labelId;
    
    /**
     * 标签类型
     */
    private String labelType;
    
    /**
     * 标签选项（仅枚举类型有值）
     */
    private List<String> labelOption;
    
    /**
     * 是否多选
     */
    private Boolean isMultiSelected;
    
    /**
     * 是否必选
     */
    private Boolean isRequired;
    
    /**
     * 列展示名
     */
    private String displayName;
    
    /**
     * 验证标注配置是否有效
     */
    public boolean isValid() {
        return labelId != null && 
               labelType != null && !labelType.trim().isEmpty() &&
               displayName != null && !displayName.trim().isEmpty();
    }
    
    /**
     * 检查是否为枚举类型的标签
     */
    public boolean isEnumType() {
        return "enum".equalsIgnoreCase(labelType) || "select".equalsIgnoreCase(labelType);
    }
    
    /**
     * 检查枚举类型是否有有效的选项
     */
    public boolean hasValidEnumOptions() {
        return isEnumType() && labelOption != null && !labelOption.isEmpty();
    }
} 