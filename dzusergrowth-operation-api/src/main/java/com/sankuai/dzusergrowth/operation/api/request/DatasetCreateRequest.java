package com.sankuai.dzusergrowth.operation.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数据集创建请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetCreateRequest {
    
    /**
     * 数据集名称（1-50字符）
     */
    private String name;
    
    /**
     * 数据集描述（0-200字符）
     */
    private String description;
    
    /**
     * 数据粒度
     */
    private String granularity; // ACTIVITY_AND_TASK, ASSET
    
    /**
     * 业务场景标识
     */
    private String businessScene;
    
    /**
     * 采集时间开始
     */
    private String timeRangeBegin;
    
    /**
     * 采集时间结束
     */
    private String timeRangeEnd;
    
    /**
     * 创建人MIS账号
     */
    private String creatorMisId;
    
    /**
     * 被采集MIS账号列表
     */
    private List<String> collectedMisId;
    
    /**
     * 采样固定指标列表
     */
    private List<String> sampleFixedMetrics;


    /**
     * 获取name
     *
     * @return name
     */
    public String getName() {
        return name;
    }

    /**
     * 获取description
     *
     * @return description
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取granularity
     *
     * @return granularity
     */
    public String getGranularity() {
        return granularity;
    }

    /**
     * 获取businessScene
     *
     * @return businessScene
     */
    public String getBusinessScene() {
        return businessScene;
    }

    /**
     * 获取timeRangeBegin
     *
     * @return timeRangeBegin
     */
    public String getTimeRangeBegin() {
        return timeRangeBegin;
    }

    /**
     * 获取timeRangeEnd
     *
     * @return timeRangeEnd
     */
    public String getTimeRangeEnd() {
        return timeRangeEnd;
    }

    /**
     * 获取creatorMisId
     *
     * @return creatorMisId
     */
    public String getCreatorMisId() {
        return creatorMisId;
    }

    /**
     * 获取collectedMisId
     *
     * @return collectedMisId
     */
    public List<String> getCollectedMisId() {
        return collectedMisId;
    }

    /**
     * 获取sampleFixedMetrics
     *
     * @return sampleFixedMetrics
     */
    public List<String> getSampleFixedMetrics() {
        return sampleFixedMetrics;
    }

    /**
     * 设置name
     *
     * @param name name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 设置description
     *
     * @param description description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 设置granularity
     *
     * @param granularity granularity
     */
    public void setGranularity(String granularity) {
        this.granularity = granularity;
    }

    /**
     * 设置businessScene
     *
     * @param businessScene businessScene
     */
    public void setBusinessScene(String businessScene) {
        this.businessScene = businessScene;
    }

    /**
     * 设置timeRangeBegin
     *
     * @param timeRangeBegin timeRangeBegin
     */
    public void setTimeRangeBegin(String timeRangeBegin) {
        this.timeRangeBegin = timeRangeBegin;
    }

    /**
     * 设置timeRangeEnd
     *
     * @param timeRangeEnd timeRangeEnd
     */
    public void setTimeRangeEnd(String timeRangeEnd) {
        this.timeRangeEnd = timeRangeEnd;
    }

    /**
     * 设置creatorMisId
     *
     * @param creatorMisId creatorMisId
     */
    public void setCreatorMisId(String creatorMisId) {
        this.creatorMisId = creatorMisId;
    }

    /**
     * 设置collectedMisId
     *
     * @param collectedMisId collectedMisId
     */
    public void setCollectedMisId(List<String> collectedMisId) {
        this.collectedMisId = collectedMisId;
    }

    /**
     * 设置sampleFixedMetrics
     *
     * @param sampleFixedMetrics sampleFixedMetrics
     */
    public void setSampleFixedMetrics(List<String> sampleFixedMetrics) {
        this.sampleFixedMetrics = sampleFixedMetrics;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static DatasetCreateRequestBuilder builder() {
        return new DatasetCreateRequestBuilder();
    }
    
    /**
     * Builder类
     */
    public static class DatasetCreateRequestBuilder {
        private String name;
        private String description;
        private String granularity;
        private String businessScene;
        private String timeRangeBegin;
        private String timeRangeEnd;
        private String creatorMisId;
        private List<String> collectedMisId;
        private List<String> sampleFixedMetrics;
        
        public DatasetCreateRequestBuilder name(String name) {
            this.name = name;
            return this;
        }
        public DatasetCreateRequestBuilder description(String description) {
            this.description = description;
            return this;
        }
        public DatasetCreateRequestBuilder granularity(String granularity) {
            this.granularity = granularity;
            return this;
        }
        public DatasetCreateRequestBuilder businessScene(String businessScene) {
            this.businessScene = businessScene;
            return this;
        }
        public DatasetCreateRequestBuilder timeRangeBegin(String timeRangeBegin) {
            this.timeRangeBegin = timeRangeBegin;
            return this;
        }
        public DatasetCreateRequestBuilder timeRangeEnd(String timeRangeEnd) {
            this.timeRangeEnd = timeRangeEnd;
            return this;
        }
        public DatasetCreateRequestBuilder creatorMisId(String creatorMisId) {
            this.creatorMisId = creatorMisId;
            return this;
        }
        public DatasetCreateRequestBuilder collectedMisId(List<String> collectedMisId) {
            this.collectedMisId = collectedMisId;
            return this;
        }
        public DatasetCreateRequestBuilder sampleFixedMetrics(List<String> sampleFixedMetrics) {
            this.sampleFixedMetrics = sampleFixedMetrics;
            return this;
        }
        
        public DatasetCreateRequest build() {
            return new DatasetCreateRequest(name, description, granularity, businessScene, timeRangeBegin, timeRangeEnd, creatorMisId, collectedMisId, sampleFixedMetrics);
        }
    }
} 