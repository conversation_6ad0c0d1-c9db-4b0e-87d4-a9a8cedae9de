package com.sankuai.dzusergrowth.operation.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据集详情查询请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetDetailRequest {
    
    /**
     * 数据集唯一标识
     */
    private String datasetId;


    /**
     * 获取datasetId
     *
     * @return datasetId
     */
    public String getDatasetId() {
        return datasetId;
    }

    /**
     * 设置datasetId
     *
     * @param datasetId datasetId
     */
    public void setDatasetId(String datasetId) {
        this.datasetId = datasetId;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static DatasetDetailRequestBuilder builder() {
        return new DatasetDetailRequestBuilder();
    }
    
    /**
     * Builder类
     */
    public static class DatasetDetailRequestBuilder {
        private String datasetId;
        
        public DatasetDetailRequestBuilder datasetId(String datasetId) {
            this.datasetId = datasetId;
            return this;
        }
        
        public DatasetDetailRequest build() {
            return new DatasetDetailRequest(datasetId);
        }
    }
} 