package com.sankuai.dzusergrowth.operation.api.enums;

import lombok.Getter;

/**
 * 标注数据项状态枚举
 *
 * <AUTHOR> Assistant
 */
@Getter
public enum AnnotationDataItemStatusEnum {
    
    /**
     * 未标注
     */
    UN_ANNOTATED(0, "未标注"),
    
    /**
     * 标注中
     */
    ANNOTATING(1, "标注中"),
    
    /**
     * 已标注
     */
    ANNOTATED(2, "已标注");
    
    /**
     * 状态码
     */
    private final Integer code;
    
    /**
     * 状态描述
     */
    private final String description;
    
    
    /**
     * 构造函数
     *
     * @param code code
     * @param description description
     */
    AnnotationDataItemStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取code
     *
     * @return code
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取description
     *
     * @return description
     */
    public String getDescription() {
        return description;
    }
    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static AnnotationDataItemStatusEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (AnnotationDataItemStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 检查状态码是否有效
     *
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }
} 