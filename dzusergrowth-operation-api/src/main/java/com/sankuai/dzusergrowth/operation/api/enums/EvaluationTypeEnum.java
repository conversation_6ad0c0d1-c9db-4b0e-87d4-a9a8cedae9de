package com.sankuai.dzusergrowth.operation.api.enums;

import lombok.Getter;

/**
 * 评测类型枚举
 *
 * <AUTHOR> Assistant
 */
@Getter
public enum EvaluationTypeEnum {
    
    /**
     * 手动评测
     */
    MANUAL(1, "手动评测"),
    
    /**
     * 自动检验
     */
    AUTO(2, "自动检验");
    
    /**
     * 类型码
     */
    private final Integer code;
    
    /**
     * 类型描述
     */
    private final String description;
    
    
    /**
     * 构造函数
     *
     * @param code code
     * @param description description
     */
    EvaluationTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取code
     *
     * @return code
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取description
     *
     * @return description
     */
    public String getDescription() {
        return description;
    }
    /**
     * 根据类型码获取枚举
     *
     * @param code 类型码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static EvaluationTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (EvaluationTypeEnum evaluationType : values()) {
            if (evaluationType.getCode().equals(code)) {
                return evaluationType;
            }
        }
        return null;
    }
    
    /**
     * 检查类型码是否有效
     *
     * @param code 类型码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }
    
    /**
     * 是否为手动评测
     *
     * @return 是否为手动评测
     */
    public boolean isManual() {
        return this == MANUAL;
    }
    
    /**
     * 是否为自动检验
     *
     * @return 是否为自动检验
     */
    public boolean isAuto() {
        return this == AUTO;
    }
} 