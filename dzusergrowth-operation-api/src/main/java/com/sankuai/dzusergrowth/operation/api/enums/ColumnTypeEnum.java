package com.sankuai.dzusergrowth.operation.api.enums;

import lombok.Getter;

/**
 * 列类型枚举
 *
 * <AUTHOR> Assistant
 */
@Getter
public enum ColumnTypeEnum {
    
    /**
     * 生成数据
     */
    GENERATED_DATA(1, "生成数据"),
    
    /**
     * 标注结果
     */
    ANNOTATION_RESULT(2, "标注结果"),
    
    /**
     * 统计指标
     */
    STATISTICS_METRIC(3, "统计指标"),
    
    /**
     * 大模型评测指标
     */
    LLM_EVALUATION_METRIC(4, "大模型评测指标"),

    /**
     * 采集会话内容
     */
    CONVERSATION_DATA(5, "采集会话内容");
    
    /**
     * 类型码
     */
    private final Integer code;
    
    /**
     * 类型描述
     */
    private final String description;
    
    
    /**
     * 构造函数
     *
     * @param code code
     * @param description description
     */
    ColumnTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取code
     *
     * @return code
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取description
     *
     * @return description
     */
    public String getDescription() {
        return description;
    }
    /**
     * 根据类型码获取枚举
     *
     * @param code 类型码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static ColumnTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (ColumnTypeEnum columnType : values()) {
            if (columnType.getCode().equals(code)) {
                return columnType;
            }
        }
        return null;
    }
    
    /**
     * 检查类型码是否有效
     *
     * @param code 类型码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }
} 