package com.sankuai.dzusergrowth.operation.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据集DTO
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetDTO {
    
    /**
     * 数据集ID
     */
    private String datasetId;
    
    /**
     * 数据集名称
     */
    private String name;
    
    /**
     * 数据粒度
     */
    private String granularity; // ACTIVITY_AND_TASK, ASSET
    
    /**
     * 业务场景
     */
    private String businessScene;
    
    /**
     * 创建人MIS账号
     */
    private String creatorMisId;
    
    /**
     * 被采集MIS账号
     */
    private String collectedMisId;
    
    /**
     * 采样固定指标
     */
    private String sampleFixedMetrics;
    
    /**
     * 数据集描述
     */
    private String description;


    /**
     * 获取datasetId
     *
     * @return datasetId
     */
    public String getDatasetId() {
        return datasetId;
    }

    /**
     * 获取name
     *
     * @return name
     */
    public String getName() {
        return name;
    }

    /**
     * 获取granularity
     *
     * @return granularity
     */
    public String getGranularity() {
        return granularity;
    }

    /**
     * 获取businessScene
     *
     * @return businessScene
     */
    public String getBusinessScene() {
        return businessScene;
    }

    /**
     * 获取creatorMisId
     *
     * @return creatorMisId
     */
    public String getCreatorMisId() {
        return creatorMisId;
    }

    /**
     * 获取collectedMisId
     *
     * @return collectedMisId
     */
    public String getCollectedMisId() {
        return collectedMisId;
    }

    /**
     * 获取sampleFixedMetrics
     *
     * @return sampleFixedMetrics
     */
    public String getSampleFixedMetrics() {
        return sampleFixedMetrics;
    }

    /**
     * 获取description
     *
     * @return description
     */
    public String getDescription() {
        return description;
    }

    /**
     * 设置datasetId
     *
     * @param datasetId datasetId
     */
    public void setDatasetId(String datasetId) {
        this.datasetId = datasetId;
    }

    /**
     * 设置name
     *
     * @param name name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 设置granularity
     *
     * @param granularity granularity
     */
    public void setGranularity(String granularity) {
        this.granularity = granularity;
    }

    /**
     * 设置businessScene
     *
     * @param businessScene businessScene
     */
    public void setBusinessScene(String businessScene) {
        this.businessScene = businessScene;
    }

    /**
     * 设置creatorMisId
     *
     * @param creatorMisId creatorMisId
     */
    public void setCreatorMisId(String creatorMisId) {
        this.creatorMisId = creatorMisId;
    }

    /**
     * 设置collectedMisId
     *
     * @param collectedMisId collectedMisId
     */
    public void setCollectedMisId(String collectedMisId) {
        this.collectedMisId = collectedMisId;
    }

    /**
     * 设置sampleFixedMetrics
     *
     * @param sampleFixedMetrics sampleFixedMetrics
     */
    public void setSampleFixedMetrics(String sampleFixedMetrics) {
        this.sampleFixedMetrics = sampleFixedMetrics;
    }

    /**
     * 设置description
     *
     * @param description description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static DatasetDTOBuilder builder() {
        return new DatasetDTOBuilder();
    }
    
    /**
     * Builder类
     */
    public static class DatasetDTOBuilder {
        private String datasetId;
        private String name;
        private String granularity;
        private String businessScene;
        private String creatorMisId;
        private String collectedMisId;
        private String sampleFixedMetrics;
        private String description;
        
        public DatasetDTOBuilder datasetId(String datasetId) {
            this.datasetId = datasetId;
            return this;
        }
        public DatasetDTOBuilder name(String name) {
            this.name = name;
            return this;
        }
        public DatasetDTOBuilder granularity(String granularity) {
            this.granularity = granularity;
            return this;
        }
        public DatasetDTOBuilder businessScene(String businessScene) {
            this.businessScene = businessScene;
            return this;
        }
        public DatasetDTOBuilder creatorMisId(String creatorMisId) {
            this.creatorMisId = creatorMisId;
            return this;
        }
        public DatasetDTOBuilder collectedMisId(String collectedMisId) {
            this.collectedMisId = collectedMisId;
            return this;
        }
        public DatasetDTOBuilder sampleFixedMetrics(String sampleFixedMetrics) {
            this.sampleFixedMetrics = sampleFixedMetrics;
            return this;
        }
        public DatasetDTOBuilder description(String description) {
            this.description = description;
            return this;
        }
        
        public DatasetDTO build() {
            return new DatasetDTO(datasetId, name, granularity, businessScene, creatorMisId, collectedMisId, sampleFixedMetrics, description);
        }
    }
} 