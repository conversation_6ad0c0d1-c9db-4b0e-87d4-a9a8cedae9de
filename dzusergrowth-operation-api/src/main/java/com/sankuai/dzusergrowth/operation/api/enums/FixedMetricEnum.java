package com.sankuai.dzusergrowth.operation.api.enums;

import com.sankuai.dzusergrowth.operation.api.enums.DataTypeEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 固定指标枚举
 * 定义系统中所有固定的统计指标
 * 
 * <AUTHOR> Assistant
 */
@Getter
public enum FixedMetricEnum {
    
    // ============= 活动相关指标 =============
    /**
     * 活动执行时长
     */
    ACTIVITY_DURATION("1001", "activity_duration", "活动执行时长", DataTypeEnum.FLOAT),
    
    /**
     * 资产采纳率（百分比）
     */
    ASSET_ADOPTION_RATE("1002", "asset_adoption_rate", "资产采纳率", DataTypeEnum.FLOAT),
    
    /**
     * 活动订单量
     */
    ACTIVITY_ORDER_COUNT("1003", "activity_order_count", "活动订单量", DataTypeEnum.LONG),
    
    /**
     * 会场PV（页面浏览量）
     */
    VENUE_PV("1004", "venue_pv", "会场PV", DataTypeEnum.LONG),
    
    /**
     * 会场UV（独立访客数）
     */
    VENUE_UV("1005", "venue_uv", "会场UV", DataTypeEnum.LONG),
    
    /**
     * 笔记浏览量
     */
    NOTE_VIEW_COUNT("1006", "note_view_count", "笔记浏览量", DataTypeEnum.LONG),
    
    // ============= 资产相关指标 =============
    /**
     * 资产生成时长
     */
    ASSET_GENERATION_DURATION("2001", "asset_generation_duration", "资产生成时长", DataTypeEnum.FLOAT);
    
    /**
     * 指标唯一标识（字符串ID）
     */
    private final String id;
    
    /**
     * 数据库列名
     */
    private final String columnName;
    
    /**
     * 显示名称
     */
    private final String displayName;
    
    /**
     * 数据类型
     */
    private final DataTypeEnum dataType;
    
    FixedMetricEnum(String id, String columnName, String displayName, DataTypeEnum dataType) {
        this.id = id;
        this.columnName = columnName;
        this.displayName = displayName;
        this.dataType = dataType;
    }

    /**
     * 获取指标ID
     *
     * @return 指标ID
     */
    public String getId() {
        return id;
    }

    /**
     * 获取数据库列名
     *
     * @return 数据库列名
     */
    public String getColumnName() {
        return columnName;
    }

    /**
     * 获取显示名称
     *
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取数据类型
     *
     * @return 数据类型
     */
    public DataTypeEnum getDataType() {
        return dataType;
    }
    
    /**
     * 根据ID获取枚举实例
     * 
     * @param id 指标ID
     * @return 枚举实例，未找到时返回null
     */
    public static FixedMetricEnum fromId(String id) {
        if (id == null) {
            return null;
        }
        
        for (FixedMetricEnum metric : values()) {
            if (id.equals(metric.getId())) {
                return metric;
            }
        }
        return null;
    }
    
    /**
     * 获取所有指标ID列表
     */
    public static List<String> getAllIds() {
        return Arrays.asList(
            "1001", "1002", "1003", "1004", "1005", "1006", "2001"
        );
    }
    
    // ============= 业务场景指标配置 =============
    
    /**
     * 活动-活动投放场景的指标ID列表
     */
    public static final List<String> ACTIVITY_DELIVERY_METRICS = Arrays.asList(
        "1001", "1002", "1003", "1004", "1005"
    );
    
    /**
     * 活动-内容营销场景的指标ID列表
     */
    public static final List<String> CONTENT_MARKETING_METRICS = Arrays.asList(
        "1001", "1006"
    );
    
    /**
     * 资产-笔记场景的指标ID列表
     */
    public static final List<String> NOTE_ASSET_METRICS = Arrays.asList(
        "2001"
    );
    
    /**
     * 检查是否为有效的指标ID
     */
    public static boolean isValidId(String id) {
        return fromId(id) != null;
    }
    
    /**
     * 获取指标的描述信息
     */
    public String getDescription() {
        return String.format("指标[%s]: %s (%s, %s)", 
            id, displayName, columnName, dataType);
    }
} 