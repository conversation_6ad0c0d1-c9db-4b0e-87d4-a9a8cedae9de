package com.sankuai.dzusergrowth.operation.api.enums;

import lombok.Getter;

/**
 * 数据粒度枚举
 *
 * <AUTHOR> Assistant
 */
@Getter
public enum DataGranularityEnum {

    /**
     * 活动粒度
     */
    ACTIVITY(1, "活动"),

    /**
     * 任务粒度
     */
    TASK(2, "任务"),

    /**
     * 查询粒度
     */
    QUERY(3, "query"),

    /**
     * 日志粒度
     */
    LOG(4, "log"),

    /**
     * 资产粒度
     */
    ASSET(5, "asset");

    /**
     * 粒度码
     */
    private final Integer code;

    /**
     * 粒度描述
     */
    private final String description;

    /**
     * 构造函数
     *
     * @param code 粒度码
     * @param description 粒度描述
     */
    DataGranularityEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取粒度码
     *
     * @return 粒度码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取粒度描述
     *
     * @return 粒度描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据粒度码获取枚举
     *
     * @param code 粒度码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static DataGranularityEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (DataGranularityEnum granularity : values()) {
            if (granularity.getCode().equals(code)) {
                return granularity;
            }
        }
        return null;
    }
    
    /**
     * 检查粒度码是否有效
     *
     * @param code 粒度码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }
} 