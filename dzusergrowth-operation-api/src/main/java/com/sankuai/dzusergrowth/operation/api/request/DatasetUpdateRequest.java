package com.sankuai.dzusergrowth.operation.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据集更新请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetUpdateRequest {
    
    /**
     * 数据集唯一标识
     */
    private String datasetId;
    
    /**
     * 数据集名称（1-50字符）
     */
    private String name;
    
    /**
     * 数据集描述（0-200字符）
     */
    private String description;


    /**
     * 获取datasetId
     *
     * @return datasetId
     */
    public String getDatasetId() {
        return datasetId;
    }

    /**
     * 获取name
     *
     * @return name
     */
    public String getName() {
        return name;
    }

    /**
     * 获取description
     *
     * @return description
     */
    public String getDescription() {
        return description;
    }

    /**
     * 设置datasetId
     *
     * @param datasetId datasetId
     */
    public void setDatasetId(String datasetId) {
        this.datasetId = datasetId;
    }

    /**
     * 设置name
     *
     * @param name name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 设置description
     *
     * @param description description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static DatasetUpdateRequestBuilder builder() {
        return new DatasetUpdateRequestBuilder();
    }
    
    /**
     * Builder类
     */
    public static class DatasetUpdateRequestBuilder {
        private String datasetId;
        private String name;
        private String description;
        
        public DatasetUpdateRequestBuilder datasetId(String datasetId) {
            this.datasetId = datasetId;
            return this;
        }
        public DatasetUpdateRequestBuilder name(String name) {
            this.name = name;
            return this;
        }
        public DatasetUpdateRequestBuilder description(String description) {
            this.description = description;
            return this;
        }
        
        public DatasetUpdateRequest build() {
            return new DatasetUpdateRequest(datasetId, name, description);
        }
    }
} 