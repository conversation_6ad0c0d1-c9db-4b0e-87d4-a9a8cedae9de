---
description: 
globs: 
alwaysApply: true
---
# 编码约定和最佳实践

## 项目业务域

这是一个 **AI 数据标注管理平台**，主要服务于美团用户增长团队：

### 核心业务场景
- **数据标注**: 提供高效的数据标注工具和流程管理
- **数据集管理**: 统一管理各类业务数据集和配置
- **评估体系**: 构建完整的数据质量评估和监控机制

## Java 编码规范

### 类命名约定

**实体对象**:
- 领域对象 (DO): `XxxDO.java` - 如 [AnnotationTaskDO.java](mdc:dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/annotation/AnnotationTaskDO.java)
- 持久化对象 (PO): `XxxPO.java` - 如 [AnnotationDataItemPO.java](mdc:dzusergrowth-operation-infrastructure/src/main/java/com/sankuai/dzusergrowth/operation/infrastructure/dal/po/AnnotationDataItemPO.java)
- 数据传输对象 (DTO): `XxxDTO.java` - 如 [AnnotationConfigDTO.java](mdc:dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/dto/AnnotationConfigDTO.java)

**服务层**:
- 应用服务: `XxxAppService.java` / `XxxAppServiceImpl.java`
- 领域服务: `XxxDomainService.java`
- 控制器: `XxxController.java`

**数据访问层**:
- 仓储接口: `XxxRepository.java`
- 仓储实现: `XxxRepositoryImpl.java`
- Mapper: `XxxPOMapper.java`

### 枚举规范

**枚举位置**: `com.sankuai.dzusergrowth.operation.api.enums`

**核心枚举**:
- [AnnotationTaskTypeEnum.java](mdc:dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/AnnotationTaskTypeEnum.java) - 标注任务类型
- [DatasetTypeEnum.java](mdc:dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/DatasetTypeEnum.java) - 数据集类型
- [TaskStatusEnum.java](mdc:dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/TaskStatusEnum.java) - 任务状态

## 架构层次规则

### 数据流转规范

**请求处理流程**:
```
Controller → AppService → DomainService → Repository → Mapper → Database
```

**数据转换链路**:
```
DTO ←→ DO ←→ PO
```

### 依赖注入

**服务注册**:
- 使用 `@Service` 注解标注应用服务实现类
- 使用 `@Component` 注解标注工具类和转换器
- 使用 `@Repository` 注解标注仓储实现类

**示例**:
- [OfflineCodeActivityRecordAppServiceImpl.java](mdc:dzusergrowth-operation-application/src/main/java/com/sankuai/dzusergrowth/operation/application/service/impl/OfflineCodeActivityRecordAppServiceImpl.java)

## 异常处理

### 异常设计原则
- 业务异常在领域层抛出
- 应用层负责异常转换和处理
- 控制器层统一异常响应格式

## 分页查询

**分页对象**:
- [PageResult.java](mdc:dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/common/PageResult.java) - 统一分页响应格式

## 数据校验

### 请求对象校验
- 使用 JSR-303 注解进行参数校验
- 在 request 包中定义请求对象: [request/](mdc:dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/request)

### 业务规则校验
- 在领域服务中实现复杂业务规则校验
- 使用查询对象进行条件构建: [query/](mdc:dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/query)

## 配置管理

### 环境配置
- 测试环境: [test/application.properties](mdc:dzusergrowth-operation-starter/src/main/profiles/test/application.properties)
- 生产环境: [prod/application.properties](mdc:dzusergrowth-operation-starter/src/main/profiles/prod/application.properties)

### 应用信息
- [app.properties](mdc:dzusergrowth-operation-starter/src/main/resources/META-INF/app.properties) - 包含 AppKey 等核心信息

## 工具类约定

### JSON 处理
- [JsonUtils.java](mdc:dzusergrowth-operation-infrastructure/src/main/java/com/sankuai/dzusergrowth/operation/infrastructure/utils/JsonUtils.java) - 统一 JSON 处理工具

## 代码质量

### Lombok 使用
- 使用 `@Data`, `@Builder`, `@NoArgsConstructor`, `@AllArgsConstructor` 简化实体类
- 避免在接口和抽象类上使用 Lombok

### 注释规范
- 所有公共接口必须有 JavaDoc 注释
- 复杂业务逻辑需要详细的行内注释
- 数据库字段映射需要说明注释
