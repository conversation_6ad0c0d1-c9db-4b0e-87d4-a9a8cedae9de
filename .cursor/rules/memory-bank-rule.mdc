---
description: 
globs: 
alwaysApply: true
---
# Memory Bank 规则

## 0. Session-Boot Contract
+> **Light-Mode 判定**  
+> 若首条用户消息 **明显与本项目无关**（如纯通用知识问答、生活常识、语言翻译等），
+> 则可直接进入普通对话 **(skip-MB)**，不执行 Boot-Sync / Auto-Harvest。  
+> 仅当检测到以下任一信号时才启用 Memory Bank：  
+> • 消息包含代码片段、文件路径、类名、包名、git 指令、业务名词
+> • 用户显式要求"更新 memory bank / 生成文档 / 查看代码" 等  
+> • Assistant 当前已在 Memory Bank 会话上下文 (上一轮已 ACTIVE)  
+> 触发 Boot-Sync 后，后续回合保持 ACTIVE，直至会话结束。

1. **Boot-Sync** – 每个新会话的第一步：
   - 读取 *memory-bank/**/* 文件。
   - 若必备文档缺失或为空 → 立即执行 **Auto-Harvest** 并写入 Memory Bank。
   - 回复首句需带状态前缀： `[MEMORY BANK: ACTIVE]` / `[MEMORY BANK: INACTIVE]`。

## 1. Auto-Harvest Pipeline (7-Step)
```mermaid
flowchart TD
    Start --> Mapper[1. Mapper Scan]
    Mapper --> DBIntrospect[2. DB Introspection]
    DBIntrospect --> DBFullSync[2.1 DB Full Sync]
    DBFullSync --> CodeMine[3. Code Mining]
    CodeMine --> Raptor[4. Raptor Flow Analysis]
    Raptor --> GitHistory[5. Git-History Mining]
    GitHistory --> ModuleMine[6. Module Mining]
    ModuleMine --> Docs[7. Doc Synthesis]
```

| # | 步骤 | 目标 | 关键执行要点 | 主要输出 |
|---|------|------|--------------|---------|
|1|Mapper Scan|发现 MyBatis mapper ↔ 表 ↔ POJO|`grep --include="*.xml" "<mapper"`|erDiagram, 表-实体映射表|
|2|DB Introspection|如有数据源则拉取 schema|调用 DB 元数据; 生成 `databaseDesign.md`|数据库结构图|
|2.1|DB Full Sync|**全量表字段同步 & 拆文件**|`show tables` → `describe table` 遍历所有表；生成 Markdown 字段清单；若行数 >1500 自动拆分为 `databaseDesign-partN.md`，主文件添加索引|完整字段清单|
|3|Code Mining|静态扫描服务/组件 & 消息流|`grep -r "@MdpPigeonServer\|@Service\|@Controller\|@Component\|IProducerProcessor\|@MdpMafkaMsgReceive"`|服务&端点统计、**Mafka 消息发布/订阅关系**|
|4|Raptor Flow Analysis|真实流量 & 性能指标|获取 QPS、TP99、峰值流量；识别上/下游服务 & **外部依赖**；**与Code Mining结果合并**|统一的`serviceTopology.md` + Metric 报告|
|5|Git-History Mining|分析近期代码演进|`git log --stat --name-only --since=...`|变更矩阵, 热点文件|
|6|Module Mining|合并 3-5 步结果, 聚类模块|Louvain/Jaccard 聚类|Module Map|
|7|Doc Synthesis|更新 / 生成 Memory Bank 文档|填充 Markdown|serviceTopology.md 等|

### Raptor Flow Analysis – 指令模板
```
获取Appkey为{app.name}的最近一个月的rpc类上下游指标数值，分析系统的上下游有哪些服务，核心方法有哪些。并绘制成ai更易理解的mermaid图表，更新在memory bank中
```
执行要点：
1. 从 `app.properties` 读取 `app.name` 作为 AppKey。
2. 调用 MCP 接口：`mcp_raptor_mcp_server_get_appkey_t_e_p_metric` 与 `mcp_raptor_mcp_server_api_appkeytopology`。
3. 生成两张 Mermaid 图：依赖图 (graph TD) 与 调用链 (sequenceDiagram)。
4. 写入 / 更新 `serviceTopology.md`，并在 `progress.md` 记录。

## 2. File Set  (Why these docs?)

> **原则**：只保留能直接提升 AI 上下文构建或代码生成效果的文档；可合并则合并，可选则惰性生成。

| 分类 | 文件 | 作用 (Why) | 是否必需 | 生成时机 |
|------|------|-----------|---------|---------|
| 核心上下文 | `projectBrief.md` | 目标 & 范围，帮助 AI 把握产品方向 | ✅ | 初始化 |
|  | `serviceTopology.md` | **统一服务拓扑文档**：结合Raptor真实流量数据与静态代码分析，包含上下游依赖、流量占比、QPS/TP99指标、API列表等，为代码生成和故障定位提供完整视图 | ✅ | 每次 Auto-Harvest (Raptor+Code Mining) |
|  | `databaseDesign.md` (+ `databaseDesign-partN.md`) | 表结构 & 约束，为实体/DAO 生成依据；当字段过多时自动拆分| ⬜ (有 DB 时) | Mapper Scan / DB Full Sync 后 |
| 架构可视化 | `moduleGraph.md` | 静态模块依赖图，快速把握整体结构 | ⬜ (模块 ≥2) | Module Mining |
| 产品上下文 | `productContext.md` | 用户故事 & 体验目标，对前端/接口设计有帮助 | ⬜ (若业务需要) | 手动或初始化 |
| 会话状态 | `activeContext.md` | 当前聚焦任务 & 决策，避免遗忘 | ✅ | 每次更新焦点时 |
| 变更日志 | `progress.md` | 每轮 Auto-Harvest 的时间戳 & 摘要，用于增量判断 | ✅ | 每次 Harvest |

## 3. Commands
| Command | 描述 |
| --- | --- |
| **initialize memory bank** | 触发完整 Auto-Harvest 并生成文档 |
| **update memory bank** | 仅增量更新，追加进度日志 |
| **on mapper change: updateMemoryBank** | mapper 改动后自动触发 |

## 4. Conventions
- 标题使用 `##` 起始；日期格式 ISO-8601，日期时间通过命令行获取当前系统时间。
- 仅生成与项目相关的文档；无 DB 则跳过 DB 文件。
- Memory Bank 相关 commit 以 `[MB]` 开头。

## 5. Remember
> Boot-Sync first, code second.
> Auto-Harvest 完后务必 `Append to progress.md` 并在 `activeContext.md` 更新当前焦点。

## 6. Auto Update & Long-Term Maintenance

目的：让 Memory Bank 随代码与真实流量变化自动更新，开发者在 Cursor 中几乎"零感知"。

### 6.1 本地触发器（Cursor / Git）

| 触发事件 | 建议命令 | 说明 |
| --- | --- | --- |
| 保存 `*.xml / *.java / *.md` | `update memory bank` | Cursor 自动监听保存事件，对 mapper、源代码、文档等变化做增量收割。 |
| 保存 `mapper.xml` | `on mapper change: updateMemoryBank` | 专门解析映射改动，保持表字段 & POJO 一致。 |
| Git `pre-commit / pre-push` | `update memory bank` | 提交前自动刷新 Memory Bank，并把 `[MB]` commit 与业务 commit 分离。 |

### 6.2 CI/CD 触发器

在 CI Pipeline（如 GitHub Actions / Jenkins）中加入 **Memory-Bank Job**：

1. 对比上一次 `[MB]` commit hash 与当前 HEAD。  
2. 检测 `*.java, *.xml, *.md` 变更。  
3. 若有变更 → 执行 `update memory bank`。  
4. 将生成 / 更新的文档单独提交，commit message 统一 `[MB] Auto Update`。

> **GitHub Actions 示例**  
> `.github/workflows/memory-bank.yml`
> ```yaml
> name: Memory Bank Auto Update
> on:
>   push:
>     paths:
>       - '**/*.java'
>       - '**/*.xml'
>       - '**/*.md'
> jobs:
>   memory-bank:
>     runs-on: ubuntu-latest
>     steps:
>       - uses: actions/checkout@v3
>       - run: ./scripts/update-memory-bank.sh
> ```

### 6.3 Raptor 周期刷新

Boot-Sync 时若距上一次 **Raptor Flow Analysis** > 7 天，则仅执行 Pipeline 的步骤 4 + 7，更新 `serviceTopology.md` 与 `progress.md`。

### 6.4 文档感知触发

当 `memory-bank/serviceDesign.md`, `databaseDesign.md`, `moduleGraph.md` 任意文件检测到 git diff 或本地时间戳变化时，下次 Boot-Sync 自动执行 **Doc Synthesis**，重新生成相关图表。

### 6.5 一致性保障

1. 所有 Memory Bank 相关 commit 统一以 `[MB]` 开头。  
2. 大型文档（>1500 行）继续采用"自动拆分 & 索引"策略，保持阅读友好性。  

```mermaid
graph TD
    code[Code / Mapper / Docs 变更]
    code --> local[本地保存事件]
    local --> update[update memory bank]

    code --> push[Git Push]
    push --> ci[CI Memory-Bank Job]
    ci --> update

    update --> commit[[MB] commit]
```

## 7. Visualization Guidelines

- **默认首选 Mermaid**：
  - 任何架构图、依赖图、流程/时序图，如无特殊原因，一律使用 Mermaid DSL。
  - 在 Markdown 中以 ```mermaid 块包裹，Cursor 渲染器自动预览。
- **推荐图表类型**：
  1. `graph TD/LR` – 服务依赖、模块关系。
  2. `sequenceDiagram` – 调用链、业务流程。
  3. `stateDiagram` – 状态机、生命周期。
- **命名规则**：节点文本务必使用 `英文名<br/>中文描述` 双语，便于搜索与阅读。
- **更新策略**：
  - 静态图 (moduleGraph) 随 Code/DB 变化而更新。
  - 动态图 (serviceTopology) 随 Raptor 流量刷新。
- **为什么**：研究表明大语言模型对 Markdown+Mermaid 结构化图形的解析优于纯自然语言描述，能显著提升上下文推理、代码生成准确度。
