---
description: 
globs: 
alwaysApply: true
---
# 项目结构规则

## 项目基本信息

- **项目名称**: dzusergrowth-operation (用户增长运营平台)
- **AppKey**: com.sankuai.dzusergrowth.operation  
- **技术栈**: Java + Spring Boot + MyBatis + Maven
- **架构**: DDD (领域驱动设计)

## 文件组织规约

### Maven 多模块结构
```
dzusergrowth-operation/
├── [pom.xml](mdc:pom.xml) - 父级 POM
├── dzusergrowth-operation-api/ - API 模块
├── dzusergrowth-operation-application/ - 应用层
├── dzusergrowth-operation-domain/ - 领域层  
├── dzusergrowth-operation-infrastructure/ - 基础设施层
└── dzusergrowth-operation-starter/ - 启动模块
```

### 关键配置文件

**应用配置**:
- [app.properties](mdc:dzusergrowth-operation-starter/src/main/resources/META-INF/app.properties) - 应用元信息
- [application.properties](mdc:dzusergrowth-operation-starter/src/main/profiles/test/application.properties) - 环境配置

**构建配置**:
- [pom.xml](mdc:pom.xml) - Maven 父级配置
- [plusboot.yaml](mdc:plusboot.yaml) - 部署配置
- [manifest.yaml](mdc:manifest.yaml) - 服务清单

**部署脚本**:
- [deploy/](mdc:deploy) - 部署相关脚本

### 包命名约定

所有 Java 包都以 `com.sankuai.dzusergrowth.operation` 开头：

**API 模块**:
- `com.sankuai.dzusergrowth.operation.api.dto` - 数据传输对象
- `com.sankuai.dzusergrowth.operation.api.request` - 请求对象  
- `com.sankuai.dzusergrowth.operation.api.response` - 响应对象
- `com.sankuai.dzusergrowth.operation.api.enums` - 枚举定义

**应用层**:
- `com.sankuai.dzusergrowth.operation.application.service` - 应用服务接口
- `com.sankuai.dzusergrowth.operation.application.service.impl` - 应用服务实现

**领域层**:
- `com.sankuai.dzusergrowth.operation.domain.model` - 领域模型
- `com.sankuai.dzusergrowth.operation.domain.repository` - 仓储接口
- `com.sankuai.dzusergrowth.operation.domain.service` - 领域服务

**基础设施层**:
- `com.sankuai.dzusergrowth.operation.infrastructure.dal` - 数据访问层
- `com.sankuai.dzusergrowth.operation.infrastructure.repository.impl` - 仓储实现
- `com.sankuai.dzusergrowth.operation.infrastructure.converter` - 数据转换器

**启动层**:
- `com.sankuai.dzusergrowth.operation.starter.controller` - Web 控制器

## 核心入口文件

**应用启动**:
- [ApplicationLoader.java](mdc:dzusergrowth-operation-starter/src/main/java/com/sankuai/dzusergrowth/ApplicationLoader.java) - 主启动类

**主要控制器**:
- [OfflineCodeActivityRecordController.java](mdc:dzusergrowth-operation-starter/src/main/java/com/sankuai/dzusergrowth/operation/starter/controller/OfflineCodeActivityRecordController.java)

**应用服务**:
- [OfflineCodeActivityRecordAppService.java](mdc:dzusergrowth-operation-application/src/main/java/com/sankuai/dzusergrowth/operation/application/service/OfflineCodeActivityRecordAppService.java)

## Memory Bank

项目维护了完整的 Memory Bank 文档：
- [memory-bank/](mdc:memory-bank) - 项目知识库
- [projectBrief.md](mdc:memory-bank/projectBrief.md) - 项目概述
- [databaseDesign.md](mdc:memory-bank/databaseDesign.md) - 数据库设计
- [serviceTopology.md](mdc:memory-bank/serviceTopology.md) - 服务拓扑
