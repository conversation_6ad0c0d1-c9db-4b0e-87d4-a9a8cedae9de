---
description: 当涉及实体字段的新增、修改或删除时，必须按照以下顺序进行操作，确保数据库、代码和配置文件的一致性。
globs: 
alwaysApply: false
---
# 数据库和 MyBatis 规则

## 数据库设计概览

项目使用 MySQL 数据库，包含 7 个核心业务表，所有表名以 `ai_admin_` 前缀开头：

### 核心数据表

**标注相关表**:
- `ai_admin_annotation_task` - 标注任务表
- `ai_admin_annotation_data_item` - 标注数据项表  
- `ai_admin_annotation_label` - 标注标签表

**数据集相关表**:
- `ai_admin_dataset` - 数据集表
- `ai_admin_data_item` - 数据项表
- `ai_admin_dataset_column_info` - 数据集列信息表

**评估相关表**:
- `ai_admin_evaluation_task` - 评估任务表

## MyBatis 配置

### Mapper 文件位置
所有 MyBatis Mapper XML 文件位于：
- [mappers/](mdc:dzusergrowth-operation-infrastructure/src/main/resources/mappers) - Mapper XML 文件目录

**核心 Mapper 文件**:
- [AnnotationDataItemPOMapper.xml](mdc:dzusergrowth-operation-infrastructure/src/main/resources/mappers/AnnotationDataItemPOMapper.xml)
- [AnnotationTaskPOMapper.xml](mdc:dzusergrowth-operation-infrastructure/src/main/resources/mappers/AnnotationTaskPOMapper.xml)
- [DatasetPOMapper.xml](mdc:dzusergrowth-operation-infrastructure/src/main/resources/mappers/DatasetPOMapper.xml)
- [DataItemPOMapper.xml](mdc:dzusergrowth-operation-infrastructure/src/main/resources/mappers/DataItemPOMapper.xml)

### 持久化对象 (PO)

**PO 类位置**: `com.sankuai.dzusergrowth.operation.infrastructure.dal.po`

**核心 PO 类**:
- [AnnotationDataItemPO.java](mdc:dzusergrowth-operation-infrastructure/src/main/java/com/sankuai/dzusergrowth/operation/infrastructure/dal/po/AnnotationDataItemPO.java)
- [AnnotationTaskPO.java](mdc:dzusergrowth-operation-infrastructure/src/main/java/com/sankuai/dzusergrowth/operation/infrastructure/dal/po/AnnotationTaskPO.java)
- [DatasetPO.java](mdc:dzusergrowth-operation-infrastructure/src/main/java/com/sankuai/dzusergrowth/operation/infrastructure/dal/po/DatasetPO.java)

### Mapper 接口

**Mapper 接口位置**: `com.sankuai.dzusergrowth.operation.infrastructure.dal.mapper`

**核心 Mapper 接口**:
- [AnnotationDataItemPOMapper.java](mdc:dzusergrowth-operation-infrastructure/src/main/java/com/sankuai/dzusergrowth/operation/infrastructure/dal/mapper/AnnotationDataItemPOMapper.java)
- [AnnotationTaskPOMapper.java](mdc:dzusergrowth-operation-infrastructure/src/main/java/com/sankuai/dzusergrowth/operation/infrastructure/dal/mapper/AnnotationTaskPOMapper.java)
- [DatasetPOMapper.java](mdc:dzusergrowth-operation-infrastructure/src/main/java/com/sankuai/dzusergrowth/operation/infrastructure/dal/mapper/DatasetPOMapper.java)

### Example 查询对象

**Example 类位置**: `com.sankuai.dzusergrowth.operation.infrastructure.dal.example`

**核心 Example 类**:
- [AnnotationDataItemPOExample.java](mdc:dzusergrowth-operation-infrastructure/src/main/java/com/sankuai/dzusergrowth/operation/infrastructure/dal/example/AnnotationDataItemPOExample.java)
- [DatasetPOExample.java](mdc:dzusergrowth-operation-infrastructure/src/main/java/com/sankuai/dzusergrowth/operation/infrastructure/dal/example/DatasetPOExample.java)

## 数据转换规则

### 转换器模式

**转换器位置**: `com.sankuai.dzusergrowth.operation.infrastructure.converter`

**核心转换器**:
- [AnnotationDataItemConverter.java](mdc:dzusergrowth-operation-infrastructure/src/main/java/com/sankuai/dzusergrowth/operation/infrastructure/converter/AnnotationDataItemConverter.java)
- [DatasetConverter.java](mdc:dzusergrowth-operation-infrastructure/src/main/java/com/sankuai/dzusergrowth/operation/infrastructure/converter/DatasetConverter.java)

### 仓储实现

**仓储实现位置**: `com.sankuai.dzusergrowth.operation.infrastructure.repository.impl`

**核心仓储实现**:
- [AnnotationDataItemRepositoryImpl.java](mdc:dzusergrowth-operation-infrastructure/src/main/java/com/sankuai/dzusergrowth/operation/infrastructure/repository/impl/AnnotationDataItemRepositoryImpl.java)
- [DatasetRepositoryImpl.java](mdc:dzusergrowth-operation-infrastructure/src/main/java/com/sankuai/dzusergrowth/operation/infrastructure/repository/impl/DatasetRepositoryImpl.java)

## 标准字段约定

所有数据表都包含以下标准审计字段：

- `id` (BIGINT) - 主键，自增
- `add_time` (TIMESTAMP) - 创建时间
- `update_time` (TIMESTAMP) - 更新时间  
- `creator_id` (BIGINT) - 创建人ID
- `updater_id` (BIGINT) - 更新人ID

## 配置文件

**MyBatis 生成器配置**:
- [generatorConfig.xml](mdc:dzusergrowth-operation-infrastructure/src/main/resources/generatorConfig.xml) - 代码生成配置
