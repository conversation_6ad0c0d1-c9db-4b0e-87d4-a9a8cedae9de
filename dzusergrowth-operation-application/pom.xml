<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.dzusergrowth</groupId>
        <artifactId>dzusergrowth-operation</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>dzusergrowth-operation-application</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>dzusergrowth-operation-application</name>

    <dependencies>
        <!-- Project module -->
        <dependency>
            <groupId>com.sankuai.dzusergrowth</groupId>
            <artifactId>dzusergrowth-operation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzusergrowth</groupId>
            <artifactId>dzusergrowth-operation-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caucho</groupId>
            <artifactId>hessian</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
    </dependencies>
</project>
