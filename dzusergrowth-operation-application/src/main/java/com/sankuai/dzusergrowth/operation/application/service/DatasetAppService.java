package com.sankuai.dzusergrowth.operation.application.service;

import com.sankuai.dzusergrowth.operation.api.common.PageResult;
import com.sankuai.dzusergrowth.operation.api.request.*;
import com.sankuai.dzusergrowth.operation.api.response.DatasetDTO;
import com.sankuai.dzusergrowth.operation.api.response.DatasetDetailResponse;

/**
 * 数据集应用服务接口
 * 提供数据集管理的RPC接口
 *
 * <AUTHOR> Assistant
 */
public interface DatasetAppService {
    
    /**
     * 获取数据集列表
     * 支持分页和多维度筛选
     *
     * @param request 查询请求
     * @return 分页数据集列表
     */
    PageResult<DatasetDTO> list(DatasetListRequest request);
    
    /**
     * 新增数据集
     * 支持活动任务和资产两种粒度类型
     *
     * @param request 创建请求
     * @return 数据集ID
     */
    String create(DatasetCreateRequest request);
    
    /**
     * 编辑数据集
     * 仅允许修改数据集的名称和描述信息
     *
     * @param request 更新请求
     * @return 是否成功
     */
    Boolean update(DatasetUpdateRequest request);
    
    /**
     * 获取数据集详情
     * 支持新字段与结构动态扩展
     *
     * @param request 详情查询请求
     * @return 数据集详情
     */
    DatasetDetailResponse detail(DatasetDetailRequest request);
} 