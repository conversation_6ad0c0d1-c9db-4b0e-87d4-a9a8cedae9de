package com.sankuai.dzusergrowth.operation.application.service;

import com.sankuai.dzusergrowth.operation.api.common.PageResult;
import com.sankuai.dzusergrowth.operation.api.request.OfflineCodeActivityRecordQueryRequest;
import com.sankuai.dzusergrowth.operation.api.response.OfflineCodeActivityRecordDTO;

public interface OfflineCodeActivityRecordAppService {
    PageResult<OfflineCodeActivityRecordDTO> queryActivityRecords(OfflineCodeActivityRecordQueryRequest request);
}
