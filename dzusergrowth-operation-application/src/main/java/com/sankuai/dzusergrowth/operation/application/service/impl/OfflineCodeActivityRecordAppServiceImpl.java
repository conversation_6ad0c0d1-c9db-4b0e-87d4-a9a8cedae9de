package com.sankuai.dzusergrowth.operation.application.service.impl;

import org.springframework.stereotype.Service;
import com.sankuai.dzusergrowth.operation.api.common.PageResult;
import com.sankuai.dzusergrowth.operation.api.request.OfflineCodeActivityRecordQueryRequest;
import com.sankuai.dzusergrowth.operation.api.response.OfflineCodeActivityRecordDTO;
import com.sankuai.dzusergrowth.operation.application.service.OfflineCodeActivityRecordAppService;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OfflineCodeActivityRecordAppServiceImpl implements OfflineCodeActivityRecordAppService {

    @Override
    public PageResult<OfflineCodeActivityRecordDTO> queryActivityRecords(OfflineCodeActivityRecordQueryRequest request) {
        return null;
    }
}
